{"extends": ["stylelint-config-standard-scss"], "plugins": ["stylelint-selector-bem-pattern", "./vendor/visymo/frontend-coding-standards/dist/stylelint/visymo-ruleset.js", "stylelint-prettier"], "ignoreFiles": [], "rules": {"prettier/prettier": [true, {"functionNameCase": "lower", "valueKeywordCase": "lower", "arrowParens": "always", "endOfLine": "lf", "printWidth": 100, "singleQuote": false, "trailingComma": "all", "overrides": [{"files": ["package.json", "package-lock.json", "*.md"], "options": {"printWidth": 80, "singleQuote": false, "tabWidth": 2, "trailingComma": "none", "useTabs": false}}]}], "alpha-value-notation": null, "at-rule-disallowed-list": ["debug"], "at-rule-no-unknown": null, "at-rule-no-vendor-prefix": true, "color-function-notation": null, "color-hex-length": "long", "color-named": "never", "color-no-invalid-hex": true, "comment-no-empty": true, "custom-property-pattern": null, "declaration-block-single-line-max-declarations": 1, "declaration-empty-line-before": "never", "declaration-no-important": true, "function-disallowed-list": ["rgb"], "length-zero-no-unit": true, "max-nesting-depth": [4, {"ignore": ["pseudo-classes"]}], "no-descending-specificity": true, "no-duplicate-selectors": null, "plugin/selector-bem-pattern": {"componentName": "^[a-z\\-][a-z0-9\\-]+$", "componentSelectors": {"initial": "^\\.{componentName}(?:(--|__)[a-z\\-][a-z0-9\\-]+)?$", "combined": "^\\.{componentName}(?:(--|__)[a-z\\-][a-z0-9\\-]+)?$"}, "ignoreSelectors": ["^th$", "^td$", "^tr$", "^col$", "^thead", "^tbody$", "^tfoot$", "^colgroup$", "^\\.[a-z\\-]+\\[disabled\\]$", "^\\#\\{\\$self\\}__[a-z-\\-]+", "^input$"], "ignoreCustomProperties": ["^(--)[^A-Z0-9:]+$"], "preset": "bem"}, "property-no-vendor-prefix": null, "rule-empty-line-before": ["always", {"ignore": ["after-comment", "first-nested"]}], "scss/dollar-variable-colon-space-after": "always", "scss/dollar-variable-colon-space-before": "never", "scss/double-slash-comment-whitespace-inside": "always", "scss/no-duplicate-dollar-variables": true, "scss/no-global-function-names": null, "selector-attribute-quotes": "always", "selector-class-pattern": ["^([a-z][a-z0-9]*)(-[a-z0-9]+)*(--[a-z0-9-]+)?$", {"message": "Expected class selector to be kebab-case"}], "selector-max-type": 3, "selector-max-universal": 1, "selector-pseudo-element-colon-notation": "double", "selector-type-case": "lower", "shorthand-property-no-redundant-values": null, "unit-disallowed-list": ["px", "cm", "ex", "in", "mm", "pc", "pt", "em"], "value-keyword-case": "lower", "value-no-vendor-prefix": null, "visymo/require-bem-block-define": true, "visymo/sort-properties-alphabetically": true, "visymo/variable-must-adhere-to-pattern": true}}
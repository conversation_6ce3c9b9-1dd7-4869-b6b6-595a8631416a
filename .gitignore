/bin/*
!/bin/console
!/bin/update-supervisor
/node_modules/
/public/build/
.yarn/*
!.yarn/cache
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

###> Database dump
/acceptation_database_dump.sql

###> symfony/framework-bundle ###
/.env
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
###< symfony/framework-bundle ###

###> squizlabs/php_codesniffer ###
/.phpcs-cache
/phpcs.xml
###< squizlabs/php_codesniffer ###

###> phpstan/phpstan ###
phpstan.neon
###< phpstan/phpstan ###

###> visymo/coding-standards ###
/visymo-workspace.json
###< visymo/coding-standards ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
.phpunit.cache
###< phpunit/phpunit ###

###> idea ###
.idea
###< idea ###

###> symfony/asset-mapper ###
/public/assets/
/assets/vendor/
###< symfony/asset-mapper ###

// Font family
$font-family-monospace: ui-monospace, monospace;
$font-family-sans-serif: arial, helvetica, sans-serif;
$font-family-system: system-ui, -apple-system, blinkmacsystemfont, "Segoe UI", roboto, oxygen-sans, ubuntu, cantarell,
"Helvetica Neue", sans-serif;

/*
    Media queries
    Range: a to f = small to large
    a: Mobile Portrait
    b: Mobile Landscape
    c: Tablet Portrait
    d: Tablet Landscape
    e: Desktop / Laptop
    f: Desktop Large
*/

// stylelint-disable unit-disallowed-list
$media-min: (
    a: "(min-width: 0px)",
    b: "(min-width: 500px)",
    c: "(min-width: 768px)",
    d: "(min-width: 992px)",
    e: "(min-width: 1200px)",
    f: "(min-width: 1400px)",
) !default;
$media-max: (
    a: "(max-width: 499px)",
    b: "(max-width: 767px)",
    c: "(max-width: 991px)",
    d: "(max-width: 1199px)",
    e: "(max-width: 1399px)"
) !default;
$media-width: (
    b: 500px,
    c: 768px,
    d: 992px,
    e: 1200px,
    f: 1400px
) !default;
$media: (
    a: map-get($media-max, a),
    b: "#{map-get($media-min, b)} and #{map-get($media-max, b)}",
    c: "#{map-get($media-min, c)} and #{map-get($media-max, c)}",
    d: "#{map-get($media-min, d)} and #{map-get($media-max, d)}",
    e: "#{map-get($media-min, e)} and #{map-get($media-max, e)}",
    f: map-get($media-min, f)
) !default;

// stylelint-enable unit-disallowed-list

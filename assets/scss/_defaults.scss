@import "variables";

// stylelint-disable visymo/require-bem-block-define
* {
    &,
    &::before,
    &::after {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
        vertical-align: top;
    }
}

html {
    font-size: 62.5%;
    padding: env(safe-area-inset);
    text-align: left;

    // Prevent webkit browsers to scale up font size to readable size
    // As example this happens in Safari on mobile in landscape mode
    -webkit-text-size-adjust: none;
}

body {
    background-color: var(--main_background-color, #f8f7fa);
    font: 400 1.4rem/1.7rem "Public Sans", sans-serif, -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    -webkit-font-smoothing: antialiased;
    color: var(--text_color, #111111);

    --link_color: #2f2b3d;

    &.theme--dark {
        --main_background-color: #121212;
        --navigation_background-color: #282828;
        --text_color: #fcfbfd;
        --link_color: #e9e3fd;
        --link-active_color: #121212;
        --link-highlight_color: #121212;
        --card_background-color: #282828;
        --header-image_background-color: #9f9b9b;
        --table-row_background-color: #000000;
        --table-highlight_background-color: #2e2e2e;
        --table-alternate_background-color: #212529;
        --table-alternate-highlight_background-color: #323232;
        --table-disabled_background-color: #5e5e5e;
        --table-link_color: var(--link_color);
        --form-label_color: #9f9b9b;
        --select_background-color: #282828;
        --select_color: var(--text_color);
        --brand-image_background-color: #fcfbfd;
        --select-option_color: #fcfbfd;
        --select-option_background-color: #282828;
        --input-disabled_background-color: #9f9b9b;
    }
}

html,
body {
    height: 100%;
    width: 100%;
}

img {
    border: 0;
    display: block;
    font-size: .1rem;
    overflow: hidden;
}

table {
    border-collapse: collapse;
    width: 100%;
}

th,
td {
    text-align: left;
}

input {
    background-color: var(--input_background-color, initial);
    color: var(--text_color);
}

// Turns off iOS and Android default roundings.
// http://stackoverflow.com/questions/2918707/turn-off-iphone-safari-input-element-rounding
button,
input {
    -webkit-appearance: none;
    background-image: none;
    border-radius: 0;
}

button {
    border: 0;
    border-collapse: separate;
    cursor: pointer;
    outline: 0;

    &::-moz-focus-inner {
        border: 0;
        padding: 0;
    }
}

ul {
    list-style: none;
}

div {
    display: block;
}

a {
    text-decoration: none;
}

p {
    margin-bottom: 2rem;
}

/** @define overlay-menu */
.overlay-menu {
    background: transparent;
    bottom: 0;
    display: block;
    height: 100%;
    left: 0;
    max-width: 40rem;
    overflow: hidden;
    position: fixed;
    right: 4rem;
    z-index: 1500;

    &--hidden {
        display: none;
    }

    &__inner {
        background: var(--navigation_background-color, #ffffff);
        height: 100%;
        inset: 0;
        -webkit-overflow-scrolling: touch;
        overflow: hidden auto;
        padding: 2.5rem 1rem;
        position: relative;
    }

    &__header {
        margin-bottom: 2rem;
    }

    &__logo {
        border-radius: 0.75rem;
        background-color: var(--header-image_background-color, transparent);
        margin: 0 auto;
    }
}

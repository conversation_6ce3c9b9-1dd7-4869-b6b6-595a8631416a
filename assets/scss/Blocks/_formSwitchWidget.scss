/** @define form-switch-widget */
.form-switch-widget {
    --_lever_height: 2rem;
    --_lever_point_size: 1.6rem;
    --_lever_width: 4rem;
    --_lever_horizontal-gutter: 0.2rem;
}

.form-switch-widget {
    display: inline-block;
    -webkit-tap-highlight-color: transparent;

    &__checkbox {
        height: 0;
        opacity: 0;
        pointer-events: none;
        position: absolute;
        width: 0;
    }

    &__lever {
        background: #dddddd;
        border-radius: calc(var(--_lever_height) / 2);
        cursor: pointer;
        display: block;
        height: var(--_lever_height);
        margin: 0;
        position: relative;
        transition: background .3s ease;
        vertical-align: middle;
        width: var(--_lever_width);

        &::after {
            background-color: #ffffff;
            transition: .3s;
        }

        &::before,
        &::after {
            border-radius: 50%;
            content: "";
            height: var(--_lever_point_size);
            left: var(--_lever_horizontal-gutter);
            position: absolute;
            top: 0.2rem;
            width: var(--_lever_point_size);
        }
    }

    &__checkbox:checked + &__lever {
        background: #198754;

        &::before,
        &::after {
            left: calc(var(--_lever_width) - var(--_lever_point_size) - var(--_lever_horizontal-gutter));
        }

        &::after {
            background-color: #ffffff;
        }
    }

    &__checkbox:disabled + &__lever {
        cursor: default;
    }
}

<?php

declare(strict_types=1);

namespace Artemis\DevelopBundle\Tests\Integration\AntelopeBrand\Console;

use PHPUnit\Framework\TestCase;
use Symfony\Component\Console\Command\Command;

class UpdateAntelopeBrandJsonSchemaConsoleTest extends TestCase
{
    public function testUpdateRequired(): void
    {
        $command = 'bin/console develop:antelope-brand:update-json-schema --dry --env=dev';

        exec($command, $output, $resultCode);
        self::assertSame(Command::SUCCESS, $resultCode);
    }
}

<?php

declare(strict_types=1);

namespace Artemis\DevelopBundle\Tests\Integration\BrandAssets\Validator;

use Infrastructure\Service\BrandAssets\Validator\BrandAssetsConfigValidator;
use Infrastructure\Service\BrandAssets\Validator\Exception\BrandAssetsValidationFailedException;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;

class BrandAssetsConfigValidatorTest extends AbstractSymfonyIntegrationTest
{
    private BrandAssetsConfigValidator $brandAssetsConfigValidator;

    protected function setUp(): void
    {
        parent::setUp();

        /** @var BrandAssetsConfigValidator $brandAssetsConfigValidator */
        $brandAssetsConfigValidator = self::getContainer()->get(
            BrandAssetsConfigValidator::class
        );
        $this->brandAssetsConfigValidator = $brandAssetsConfigValidator;
    }

    public function testValidConfig(): void
    {
        $this->expectNotToPerformAssertions();

        $config = (string)file_get_contents(__DIR__.'/_data/valid.json');
        $config = json_decode($config, true, 512, JSON_THROW_ON_ERROR);

        $this->brandAssetsConfigValidator->validate($config);
    }

    public function testInvalidConfig(): void
    {
        $this->expectException(BrandAssetsValidationFailedException::class);

        $config = (string)file_get_contents(__DIR__.'/_data/invalid.json');
        $config = json_decode($config, true, 512, JSON_THROW_ON_ERROR);

        $this->brandAssetsConfigValidator->validate($config);
    }
}

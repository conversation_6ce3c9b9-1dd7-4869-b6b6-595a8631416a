<?php

declare(strict_types=1);

namespace Artemis\DevelopBundle;

use Artemis\DevelopBundle\DependencyInjection\DevelopExtension;
use Symfony\Component\DependencyInjection\Extension\ExtensionInterface;
use Symfony\Component\HttpKernel\Bundle\AbstractBundle;

class DevelopBundle extends AbstractBundle
{
    public function getContainerExtension(): ?ExtensionInterface
    {
        return new DevelopExtension();
    }
}

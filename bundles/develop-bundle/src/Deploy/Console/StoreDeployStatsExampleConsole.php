<?php

declare(strict_types=1);

namespace Artemis\DevelopBundle\Deploy\Console;

use Application\Command\Deploy\StoreDeployStats\StoreDeployStatsCommand;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\CommandBus\Domain\CommandBus\DirectCommandBusInterface;

#[AsCommand(
    name: 'develop:deploy:store-deploy-stats-example',
)]
class StoreDeployStatsExampleConsole extends Command
{
    public function __construct(
        private readonly DirectCommandBusInterface $directCommandBus
    )
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $config = (string)file_get_contents(__DIR__.'/deploy_stats/quick-reaction-force-test-deploy.json');
        $config = json_decode($config, true, 512, JSO<PERSON>_THROW_ON_ERROR);

        $this->directCommandBus->handle(
            new StoreDeployStatsCommand(
                data: $config
            )
        );

        return Command::SUCCESS;
    }
}

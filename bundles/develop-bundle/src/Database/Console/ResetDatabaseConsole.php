<?php

declare(strict_types=1);

namespace Artemis\DevelopBundle\Database\Console;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\ArrayInput;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

#[AsCommand(
    name: 'develop:database:reset'
)]
final class ResetDatabaseConsole extends Command
{
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $application = $this->getApplication();

        if ($application === null) {
            throw new \RuntimeException('Application is not set');
        }

        // Create database
        $this->executeSqlFile(
            sprintf('%s/schema.sql', $this->getSchemaPath()),
            $output
        );

        // Import deploy stats example
        $application->doRun(
            new ArrayInput(
                [
                    'command' => 'develop:deploy:store-deploy-stats-example',
                ]
            ),
            $output
        );

        return Command::SUCCESS;
    }

    private function getSchemaPath(): string
    {
        return sprintf('%s/Schema', dirname(__DIR__));
    }

    private function executeSqlFile(string $file, OutputInterface $output): void
    {
        $output->writeln(
            sprintf('Executing SQL file %s', $file)
        );
        $result = shell_exec(sprintf('mariadb < %s', $file));

        if ($result === false) {
            throw new \RuntimeException(
                sprintf('Failed to execute SQL file %s', $file)
            );
        }
    }
}

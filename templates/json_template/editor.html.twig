{# @var json_template_url string #}
{% extends 'base.html.twig' %}

{% block body %}
    <twig:Card>
        <twig:ButtonLink confirm url="{{ path('route_json_template_schema_update') }}">Update JSON schema</twig:ButtonLink>

        {{ form_start(form, {'attr': {'class': 'form--inline card__content--right'}}) }}
            {{ form_widget(form) }}
        {{ form_end(form) }}
    </twig:Card>

    <twig:JsonEditor schemaUrl="{{ json_schema_url }}">
        {{- json_template_content -}}
    </twig:JsonEditor>

    <script>
        appReady.push(function () {
            document.querySelector('form[name="json_template_preview"]')
                .addEventListener('submit', (event) => {
                    event.preventDefault();

                    const form = event.target;
                    const jsonTemplateInput = form.querySelector('#json_template_preview_json_template');
                    const slugSelect = form.querySelector('#json_template_preview_slug');
                    const localeSelect = form.querySelector('#locale');

                    jsonTemplateInput.value = window.jsonEditor.getValue();

                    const formData = new FormData();
                    formData.append('slug', slugSelect.value);
                    formData.append('locale', localeSelect.value);
                    formData.append('json_template', jsonTemplateInput.value);

                    fetch('{{ path('route_json_template_preview') }}', {
                        method: 'POST',
                        body: formData
                    })
                        .then(response => {
                            if (!response.ok) {
                                return response.json().then(data => {
                                    throw new Error(data.errors?.join("\n") || 'An unknown error occurred');
                                });
                            }

                            return response.json();
                        })
                        .then(data => {
                            if (!data.url) {
                                throw new Error('No URL returned from the server');
                            }

                            const form = document.createElement('form');
                            form.method = 'POST';
                            form.action = data.url;
                            form.target = '_blank';

                            const input = document.createElement('input');
                            input.type = 'hidden';
                            input.name = 'json_template';
                            input.value = jsonTemplateInput.value;
                            form.appendChild(input);

                            document.body.appendChild(form);
                            form.submit();
                            document.body.removeChild(form);
                        })
                        .catch(error => alert(error.message));
                });
        });
    </script>
{% endblock %}

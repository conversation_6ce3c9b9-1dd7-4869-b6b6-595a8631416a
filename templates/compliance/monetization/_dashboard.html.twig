{# @var brands Domain\Brand\Brand[] #}
{# @var data_filter_matcher Infrastructure\Service\DataFilter\Matcher\DataFilterMatcherInterface #}
<twig:Card title="Brand monetization" modifiers="content-vertical">
    <p>
        Monetization is enabled by default. Disable ads, related terms, and/or display banners for each brand.
    </p>
    <div>
        <twig:ButtonLink url="{{ path('route_compliance_monetization_add') }}">Add</twig:ButtonLink>
    </div>
    <twig:DataFilter dataFilterMatcher="{{ data_filter_matcher }}"/>
    <twig:Table>
        <twig:TableHead modifiers="sticky">
            <tr>
                <th class="table__cell">Brand</th>
                <th class="table__cell">Partner</th>
                <th class="table__cell table__cell--align-center table__cell--attribute-property">Ads</th>
                <th class="table__cell table__cell--align-center table__cell--attribute-property">Related terms</th>
                <th class="table__cell table__cell--align-center table__cell--attribute-property">Display banners</th>
                <th class="table__cell table__cell--datetime">Updated</th>
                <th></th>
            </tr>
        </twig:TableHead>
        <tbody>
            {% for brand in brands %}
                <tr class="table__row">
                    <td class="table__cell">
                        <twig:TableLink url="{{ path('route_compliance_monetization_edit', {slug: brand.slug}) }}" title="Edit monetization for this brand">{{ brand.slug }}</twig:TableLink>
                    </td>
                    <td class="table__cell">{{ brand.partnerSlug }}</td>
                    <td class="table__cell table__cell--align-center">
                        <twig:BooleanBox value="{{ brand.monetization.adsEnabled }}"></twig:BooleanBox>
                    </td>
                    <td class="table__cell table__cell--align-center">
                        <twig:BooleanBox value="{{ brand.monetization.relatedTermsEnabled }}"></twig:BooleanBox>
                    </td>
                    <td class="table__cell table__cell--align-center">
                        <twig:BooleanBox value="{{ brand.monetization.displayBannersEnabled }}"></twig:BooleanBox>
                    </td>
                    <td class="table__cell table__cell--datetime">
                        {{- brand.monetization.updatedAt|date -}}
                    </td>
                    <td class="table__cell table__cell--no-wrap">
                        <twig:ButtonLink url="{{ path('route_brand_audit_log', {slug: brand.slug}) }}" title="Audit log" modifiers="small">
                            <i class="bi bi-file-text"></i>
                        </twig:ButtonLink>
                    </td>
                </tr>
            {% else %}
                <tr class="table__row">
                    <td colspan="100%">No brands found</td>
                </tr>
            {% endfor %}
        </tbody>
    </twig:Table>
</twig:Card>

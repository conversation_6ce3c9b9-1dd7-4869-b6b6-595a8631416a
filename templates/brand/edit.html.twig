{# @var module_field_names array<string, bool> #}
{% extends 'base.html.twig' %}
{% form_theme form _self %}

{% block body %}
    {% include 'brand/_header.html.twig' with {brand: brand} only %}
    {{ form_start(form, {attr: {novalidate: 'novalidate'}}) }}
    {{ form_errors(form) }}
    <twig:Card title="Brand" modifiers="content-vertical">
        <div class="brand_properties">
            {{ form_row(form.active) }}
        </div>
    </twig:Card>
    {% for module_field_name, has_errors in module_field_names %}
        {% set form_child = form[module_field_name] %}
        <twig:Card title="{{ field_label(form_child) }}" foldable="{{ not has_errors }}" modifiers="content-vertical">
            {{ form_errors(form_child) }}
            {{ form_widget(form_child) }}
        </twig:Card>
    {% endfor %}
    <twig:Card>
        {{ form_widget(form) }}
    </twig:Card>
    {{ form_end(form) }}
{% endblock %}

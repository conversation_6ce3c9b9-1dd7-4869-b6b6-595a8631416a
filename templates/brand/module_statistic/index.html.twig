{# @var response \Application\Query\Brand\GetBrandModuleStatistics\GetBrandModuleStatisticsResponse #}
{# @var result \Application\Query\Brand\GetBrandModuleStatistics\Result\BrandModuleStatisticResult #}
{% extends 'base.html.twig' %}

{% block body %}
    <twig:Card title="Brand module statistic">
        <twig:DataSearch target="brand" placeholder="Search for brand"/>
        <twig:ButtonLink url="{{ path('route_brand_module_statistic', {days_ago: 1}) }}" modifiers="{{ days_ago == 1 ? 'active' }}">Yesterday</twig:ButtonLink>
        <twig:ButtonLink url="{{ path('route_brand_module_statistic', {days_ago: 7}) }}" modifiers="{{ days_ago == 7 ? 'active' }}">Last 7 days</twig:ButtonLink>
        <twig:ButtonLink url="{{ path('route_brand_module_statistic', {days_ago: 30}) }}" modifiers="{{ days_ago == 30 ? 'active' }}">Last 30 days</twig:ButtonLink>
    </twig:Card>
    <twig:Card>
        <twig:Table searchTarget="brand">
            <twig:TableHead modifiers="sticky">
                <tr>
                    <th class="table__cell">Brand</th>
                    {% for module_route in response.availableBrandModuleSearchRoutes %}
                        <th colspan="2" class="table__cell table__cell--align-center" title="{{ module_route.moduleName }}">{{ module_route.searchRoute.wildcardPath }}</th>
                    {% endfor %}
                </tr>
                <tr>
                    <th></th>
                    {% for module_route in response.availableBrandModuleSearchRoutes %}
                        <th class="table__cell table__cell--border-left table__cell--align-right">
                            <i class="table__icon bi bi-robot" title="Ad bot"></i>
                        </th>
                        <th class="table__cell table__cell--border-right table__cell--align-right">
                            <i class="table__icon bi bi-person-fill" title="Visitor"></i>
                        </th>
                    {% endfor %}
                </tr>
            </twig:TableHead>
            <tbody>
                {% for result in response.results %}
                    <tr
                        data-slug="{{ result.slug }}"
                    >
                        <td class="table__cell">
                            <twig:TableLink url="{{ path('route_brand_edit', {slug: result.slug}) }}" title="Edit brand">{{ result.slug }}</twig:TableLink>
                        </td>
                        {% for module_route in response.availableBrandModuleSearchRoutes %}
                            {% set wildcard_path = module_route.searchRoute.wildcardPath %}
                            {% set ad_bot_count = result.getPathCount(true, wildcard_path) %}
                            {% set visitor_count = result.getPathCount(false, wildcard_path) %}
                            {% set title = module_route.moduleName %}
                            {% if not result.isPathValid(wildcard_path) %}
                                {% set cell_modifier = ' table__cell--bg-warning' %}
                                {% set title = title ~ ' is ' ~ (result.isPathEnabled(wildcard_path) ? 'enabled, but has no requests' : 'disabled, but has requests') %}
                            {% elseif not result.isPathEnabled(wildcard_path) %}
                                {% set cell_modifier = ' table__cell--bg-disabled' %}
                            {% else %}
                                {% set cell_modifier = '' %}
                            {% endif %}
                            <td class="table__cell table__cell--border-left table__cell--align-right{{ cell_modifier }}" title="{{ title }}">
                                {{- ad_bot_count is not null ? ad_bot_count|number_format(0, '.', '.') -}}
                            </td>
                            <td class="table__cell table__cell--border-left-dotted table__cell--border-right table__cell--align-right{{ cell_modifier }}" title="{{ title }}">
                                {{ visitor_count is not null ? visitor_count|number_format(0, '.', '.') }}
                            </td>
                        {% endfor %}
                    </tr>
                {% else %}
                    <tr>
                        <td colspan="100%">No brands found</td>
                    </tr>
                {% endfor %}
            </tbody>
        </twig:Table>
    </twig:Card>
{% endblock %}

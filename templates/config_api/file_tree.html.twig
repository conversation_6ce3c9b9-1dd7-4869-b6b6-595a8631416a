{# @var contents Visymo\ConfigApiClient\GetFileTree\Model\ContentsResult #}
{% extends 'base.html.twig' %}

{% block body %}
    <twig:Card title="Config API file tree">
        <twig:Table>
            <twig:TableHead modifiers="sticky">
                <tr>
                    <th class="table__cell">Path</th>
                    <th class="table__cell table__cell--datetime">Last modified at</th>
                </tr>
            </twig:TableHead>
            <tbody>
                {% if not contents.isEmpty() %}
                    {% include 'config_api/_contents.html.twig' with {
                        level: 0,
                        parent_path: '/',
                        contents: contents
                    } only %}
                {% else %}
                    <tr>
                        <td colspan="100%">No files found</td>
                    </tr>
                {% endif %}
            </tbody>
        </twig:Table>
    </twig:Card>
{% endblock %}

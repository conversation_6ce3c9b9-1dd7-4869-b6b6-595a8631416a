{% extends 'base.html.twig' %}

{% block body %}
    <twig:Card>
        <twig:ButtonLink url="{{ path('route_styles_style_index') }}">View per style ID</twig:ButtonLink>
    </twig:Card>
    <twig:Card>
        <twig:DataSearch target="style-ids" placeholder="Search for style ID or brand"/>
    </twig:Card>
    <twig:Card>
        <twig:Table searchTarget="style-ids">
            <twig:TableHead modifiers="sticky">
                <tr>
                    <th></th>
                    <th></th>
                    {% for brand_module_attribute, ad_sense_style_id_properties in table_headers %}
                        <th colspan="{{ ad_sense_style_id_properties|length }}">
                            {{ brand_module_attribute }}
                        </th>
                    {% endfor %}
                </tr>
                <tr>
                    <th>Brand</th>
                    <th class="table__cell table__cell--no-wrap">Unique</th>
                    {% for brand_module_attribute, ad_sense_style_id_properties in table_headers %}
                        {% for ad_sense_style_id_property in ad_sense_style_id_properties %}
                            <th>{{ ad_sense_style_id_property }}</th>
                        {% endfor %}
                    {% endfor %}
                </tr>
            </twig:TableHead>
            <tbody>
                {% for row in table_rows %}
                    <tr
                        class="table__row"
                        data-brand-slug="{{ row.brand.slug }}"
                        data-style-ids="{{ get_adsense_style_ids_per_brand(row.brand.slug)|json_encode }}">
                        <td>
                            <twig:TableLink url="{{ path('route_brand_edit', {slug: row.brand.slug}) }}" title="Edit brand">
                                {{ row.brand.slug }}
                            </twig:TableLink>
                        </td>
                        <td>
                            {%- if brand_has_unique_adsense_style_ids(row.brand.slug) -%}
                                <span class="table__info table__info--success">Yes</span>
                            {%- else -%}
                                <span class="table__info table__info--warning">No</span>
                            {%- endif -%}
                        </td>

                        {% for brand_module_property, ad_sense_style_id_property_count in row.modulePropertyMap %}
                            {% set module_property = attribute(row.brand, brand_module_property) %}
                            {% if module_property is not null and module_property.enabled %}
                                {% for adStyle in module_property.getAdSenseStyleIds() %}
                                    <td class="table__info table__info--{{ is_adsense_style_id_unique_per_brand(adStyle) ? 'success' : 'warning' }}">
                                        <a href="{{ path('route_adsense_style_id_view', {styleId: adStyle}) }}" class="table-link table-link--inherit" target="_blank">{{ adStyle }}</a>
                                    </td>
                                {% endfor %}
                            {% else %}
                                <td colspan="{{ ad_sense_style_id_property_count }}"></td>
                            {% endif %}
                        {% endfor %}
                    </tr>
                {% else %}
                    <tr class="table__row">
                        <td colspan="100%">No AdSense style ID's found</td>
                    </tr>
                {% endfor %}
            </tbody>
        </twig:Table>
    </twig:Card>
{% endblock %}

{# @var vbb_styles Domain\VbbStyleId\VbbStyleId[] #}
{# @var data_filter_matcher Infrastructure\Service\DataFilter\Matcher\DataFilterMatcherInterface #}
{% extends 'base.html.twig' %}

{% block body %}
    <twig:Card>
        <twig:ButtonLink confirm url="{{ path('route_styles_vbb_push_all') }}">Push all styles</twig:ButtonLink>
    </twig:Card>
    <twig:Card modifiers="content-vertical">
        <twig:DataFilter dataFilterMatcher="{{ data_filter_matcher }}"/>

        <twig:Table>
            <twig:TableHead modifiers="sticky">
                <tr>
                    <th>Style ID</th>
                    <th>Brand</th>
                    <th>Last imported at</th>
                    <th>Last pushed at</th>
                </tr>
            </twig:TableHead>
            <tbody>
                {% for vbb_style in vbb_styles %}
                    <tr class="table__row" data-style-id="{{ vbb_style.styleId }}" data-brand="{{ vbb_style.brand.slug }}">
                        <td>
                            <twig:TableLink url="{{ path('route_adsense_style_id_view', {styleId: vbb_style.styleId}) }}">{{ vbb_style.styleId }}</twig:TableLink>
                        </td>
                        <td>{{ vbb_style.brand.slug }}</td>
                        <td>
                            {{ vbb_style.lastImportedAt|date }}
                        </td>
                        <td>
                            {{ vbb_style.lastPushedAt ? vbb_style.lastPushedAt|date() : '-' }}
                        </td>
                    </tr>
                {% else %}
                    <tr>
                        <td colspan="100%">No styles found</td>
                    </tr>
                {% endfor %}
            </tbody>
        </twig:Table>
    </twig:Card>
{% endblock %}

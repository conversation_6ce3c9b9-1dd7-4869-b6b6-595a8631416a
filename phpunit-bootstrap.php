<?php

declare(strict_types=1);

use Symfony\Component\Dotenv\Dotenv;
use Symfony\Component\ErrorHandler\ErrorHandler;

require __DIR__.'/vendor/autoload.php';

if (file_exists(__DIR__.'/config/bootstrap.php')) {
    require __DIR__.'/config/bootstrap.php';
} elseif (method_exists(Dotenv::class, 'bootEnv')) {
    (new Dotenv())->bootEnv(__DIR__.'/.env');
}

// Fixes https://github.com/symfony/symfony/issues/53812
// @see https://github.com/symfony/symfony/issues/53812#issuecomment-1962740145
set_exception_handler([new ErrorHandler(), 'handleException']);

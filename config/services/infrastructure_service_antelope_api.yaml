services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    # Client
    infrastructure.antelope_api.psr_http_client:
        class: Psr\Http\Client\ClientInterface
        factory: [ 'Http\Adapter\Guzzle7\Client', 'createWithConfig' ]
        arguments:
            $config:
                timeout: 5
                headers: {
                    'User-Agent': 'Artemis (+https://artemis.visymo.com)'
                }

    infrastructure.antelope_api.http_client:
        class: Visymo\AntelopeApiClient\Http\Client\HttpClient
        arguments:
            $client: '@infrastructure.antelope_api.psr_http_client'
            $apiUrl: '%env(ANTELOPE_URL)%'
            $authToken: '%env(ANTELOPE_API_AUTH_TOKEN)%'

    Visymo\AntelopeApiClient\AntelopeApiClientInterface:
        class: Visymo\AntelopeApiClient\AntelopeApiClient
        arguments:
            $httpClient: '@infrastructure.antelope_api.http_client'

services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    # JSON Overrides
    infrastructure.service.json_template_overrides.array_serializer:
        class: Visymo\Serializer\NativePrettyJsonArraySerializer
        arguments:
            $unescapedSlashes: true

    infrastructure.service.json_template_overrides.file_factory:
        class: Visymo\Filesystem\SerializedFile\Type\NativePrettyJsonFileFactory
        arguments:
            $nativePrettyJsonArraySerializer: '@infrastructure.service.json_template_overrides.array_serializer'

    infrastructure.service.json_template_overrides.file:
        class: Visymo\Filesystem\SerializedFile\SerializedFileInterface
        factory: [ '@infrastructure.service.json_template_overrides.file_factory', 'create' ]
        arguments:
            $filePath: '%env(JSON_TEMPLATE_OVERRIDES_PATH)%/template_overrides.json'

    Infrastructure\Service\JsonTemplateOverrides\JsonTemplateOverrides:
        arguments:
            $templateOverridesJsonFile: '@infrastructure.service.json_template_overrides.file'

    # JSON Template Overrides Updater
    Infrastructure\Service\JsonTemplateOverrides\JsonTemplateOverridesUpdater:
        arguments:
            $templateOverridesJsonFile: '@infrastructure.service.json_template_overrides.file'

    Infrastructure\Service\JsonTemplateOverrides\JsonTemplateOverridesClient:
        arguments:
            $projectId: 211 # Brand websites

when@test:
    services:
        # TODO SF-1789: refactor config file path solutions
        infrastructure.service.json_template_overrides.file:
            class: Visymo\Filesystem\SerializedFile\SerializedFileInterface
            factory: [ '@infrastructure.service.json_template_overrides.file_factory', 'create' ]
            arguments:
                $filePath: '%kernel.project_dir%/config/json_template/template_overrides.json'

services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false

    # Client
    infrastructure.apify.http_client:
        class: Psr\Http\Client\ClientInterface
        factory: [ 'Http\Adapter\Guzzle7\Client', 'createWithConfig' ]
        arguments:
            $config: [ ]

    Infrastructure\Service\Apify\ApifyClient:
        arguments:
            $client: '@infrastructure.apify.http_client'
            $token: '%env(APIFY_TOKEN)%'
            $apiUrl: '%env(APIFY_API_URL)%'
        tags: ['scraper.client']

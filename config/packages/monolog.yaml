monolog:
    channels:
        - app # Default channel used by Symfony/Monolog
        - deprecation # Deprecations are logged in the dedicated 'deprecation' channel when it exists
    handlers:
        console:
            type: console
            channels: [ '!event' ]
            formatter: monolog_extensions.console_formatter

when@dev: &dev
    monolog:
        handlers:
            main:
                type: rotating_file
                path: '%kernel.logs_dir%/%kernel.environment%.log'
                level: notice
                max_files: 5
                channels: [ '!event', '!translation' ]

            # Uncomment to test Elastic integration on dev env
            # on dev-vm, this works better without buffer
            #            socket:
            #                type: socket
            #                connection_string: '%env(FILEBEAT_URL)%'
            #                persistent: true
            #                formatter: Visymo\MonologExtensionsBundle\Formatter\LogstashFormatter
            #                channels: [ '!event', '!deprecation' ]
            #                level: notice

            # Uncomment to test Sentry integration on dev env
#            sentry:
#                type: service
#                level: warning
#                channels: [ '!event', '!deprecation' ]
#                id: Visymo\Shared\Infrastructure\Bridge\MonologExtensions\Handler\SentryHandler

when@dev_direct: *dev

when@test:
    monolog:
        handlers:
            main:
                type: rotating_file
                path: '%kernel.logs_dir%/%kernel.environment%.log'
                level: notice
                max_files: 5
                channels: [ '!event', '!translation' ]

when@prod:
    monolog:
        handlers:
            socket_buffer:
                type: buffer
                handler: socket
            socket:
                type: socket
                connection_string: '%env(FILEBEAT_URL)%'
                persistent: false
                formatter: Visymo\MonologExtensionsBundle\Formatter\LogstashFormatter
                channels: [ '!event', '!deprecation' ]
                level: notice

            sentry_buffer:
                type: buffer
                level: warning
                channels: [ '!event', '!deprecation' ]
                handler: sentry
            sentry:
                type: service
                id: Visymo\MonologExtensionsBundle\Handler\SentryHandler

services:
    _defaults:
        autowire: true

    Monolog\Processor\PsrLogMessageProcessor:
        tags:
            - { name: monolog.processor, channel: app }

    Monolog\Processor\ProcessIdProcessor:
        tags:
            - { name: monolog.processor, channel: app }

    Visymo\MonologExtensionsBundle\Processor\ProcessArgumentsProcessor:
        tags:
            - { name: monolog.processor, channel: app }

    Visymo\MonologExtensionsBundle\Processor\RawMessageProcessor:
        tags:
            - { name: monolog.processor, channel: app }

    Visymo\CommandBus\Infrastructure\CommandBus\Middleware\DebugLoggingMiddleware\CommandBusMonologProcessor:
        tags:
            - { name: monolog.processor, channel: app }

    # Sentry
    Visymo\MonologExtensionsBundle\Formatter\SentryFormatter: ~

    Visymo\MonologExtensionsBundle\Handler\SentryHandler:
        arguments:
            - '@Sentry\State\HubInterface'
        calls:
            - [ 'setFormatter', [ '@Visymo\MonologExtensionsBundle\Formatter\SentryFormatter' ] ]

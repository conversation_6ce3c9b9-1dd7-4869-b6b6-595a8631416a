{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://www.visymo.com/brand_asset.schema.json", "definitions": {"variables": {"type": "object", "required": ["brand-primary-color"], "additionalProperties": true, "properties": {"brand-primary-color": {"type": "string", "pattern": "^#[0-9A-F]{6}$"}}}, "images": {"required": ["apple-touch-icon.png", "favicon.ico", "favicon.png", "favicon.svg", "logo-small.png", "logo-small-dark-mode.png", "logo-medium.png", "logo-medium-dark-mode.png", "logo-large.png", "logo-large-dark-mode.png", "logo.svg", "logo-dark-mode.svg"], "properties": {"apple-touch-icon.png": {"$ref": "#/definitions/base64ImagePng"}, "favicon.ico": {"$ref": "#/definitions/base64ImageIco"}, "favicon.png": {"$ref": "#/definitions/base64ImagePng"}, "favicon.svg": {"$ref": "#/definitions/base64ImageSvg"}, "logo-small.png": {"$ref": "#/definitions/base64ImagePng"}, "logo-small-dark-mode.png": {"$ref": "#/definitions/base64ImagePng"}, "logo-medium.png": {"$ref": "#/definitions/base64ImagePng"}, "logo-medium-dark-mode.png": {"$ref": "#/definitions/base64ImagePng"}, "logo-large.png": {"$ref": "#/definitions/base64ImagePng"}, "logo-large-dark-mode.png": {"$ref": "#/definitions/base64ImagePng"}, "logo.svg": {"$ref": "#/definitions/base64ImageSvg"}, "logo-dark-mode.svg": {"$ref": "#/definitions/base64ImageSvg"}, "author.jpg": {"$ref": "#/definitions/base64ImageJpg"}, "header.jpg": {"$ref": "#/definitions/base64ImageJpg"}}}, "base64ImageIco": {"type": "string", "pattern": "^data:image/ico;base64,(.+)$"}, "base64ImageJpg": {"type": "string", "pattern": "^data:image/jpg;base64,(.+)$"}, "base64ImagePng": {"type": "string", "pattern": "^data:image/png;base64,(.+)$"}, "base64ImageSvg": {"type": "string", "pattern": "^data:image/svg\\+xml;base64,(.+)$"}}, "type": "object", "required": ["variables", "images"], "properties": {"variables": {"$ref": "#/definitions/variables"}, "images": {"$ref": "#/definitions/images"}}}
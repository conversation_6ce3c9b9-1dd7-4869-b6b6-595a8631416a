{"name": "quick-reaction-force", "version": "1.0.0", "license": "UNLICENSED", "devDependencies": {"gulp": "^5.0.0", "gulp-autoprefixer": "^9.0.0", "gulp-clean": "^0.4.0", "gulp-filter": "^9.0.1", "gulp-header": "^2.0.9", "gulp-include": "^2.4.1", "gulp-mode": "^1.1.0", "gulp-rename": "^2.0.0", "gulp-rev": "^11.0.0", "gulp-rev-dist-clean": "^3.2.3", "gulp-sass": "^5.1.0", "gulp-sourcemaps": "^3.0.0", "gulp-string-replace": "^1.1.2", "gulp-uglify": "^3.0.2", "merge-stream": "^2.0.0", "node-sass": "^9.0.0", "postcss": "^8.5.3", "prettier": "^3.5.2", "stylelint": "^16.14.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.3", "stylelint-selector-bem-pattern": "^4.0.1", "yarn-upgrade-all": "^0.7.4"}, "scripts": {"dev": "gulp", "watch": "gulp watch", "production": "gulp --production", "lint-base": "stylelint ./assets/scss/**.scss", "lint-components": "stylelint ./src/Infrastructure/UserInterface/Web/Component/**/*.scss", "lint": "yarn lint-base && yarn lint-components"}, "browserslist": ["last 2 Chrome versions"], "dependencies": {"ace-builds": "^1.38.0", "ace-linters": "^1.5.0", "bootstrap-icons": "^1.11.3", "through2": "^4.0.2"}, "type": "module", "packageManager": "yarn@4.9.1"}
<?xml version="1.0"?>
<ruleset name="PHP_CodeSniffer">
    <!-- Load company standards ruleset -->
    <rule ref="./vendor/visymo/coding-standards/dist/phpcs.xml"/>
    <rule ref="Visymo.Hexagonal.HexagonalNamespaces">
        <properties>
            <property name="whitelistDomainNamespaces" type="array">
                <element value="Visymo\Filesystem"/>
            </property>
        </properties>
    </rule>

    <!-- Files to check -->
    <file>./src</file>
    <file>./tests</file>
    <file>./bundles/develop-bundle/src</file>
    <file>./bundles/develop-bundle/tests</file>

    <!-- Exclude specific folders -->
    <exclude-pattern>*/.idea/*</exclude-pattern>
    <exclude-pattern>*/vendor/*</exclude-pattern>
    <exclude-pattern>*/config/*</exclude-pattern>
    <exclude-pattern>*/public/index.php</exclude-pattern>
    <exclude-pattern>*/var/cache/*</exclude-pattern>
</ruleset>

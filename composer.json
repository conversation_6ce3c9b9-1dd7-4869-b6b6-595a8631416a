{"name": "visymo/artemis", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-curl": "*", "ext-iconv": "*", "ext-mbstring": "*", "doctrine/doctrine-bundle": "^2.14.0", "doctrine/orm": "^3.3.2", "guzzlehttp/psr7": "^2.7.1", "monolog/monolog": "^3.9.0", "nbgrp/onelogin-saml-bundle": "^2.0.2", "nyholm/psr7": "^1.8.2", "opis/json-schema": "^2.4.1", "php-http/guzzle7-adapter": "^1.1.0", "php-http/httplug": "^2.4.1", "php-webdriver/webdriver": "^1.15.2", "ramsey/uuid": "^4.7.6", "symfony/asset": "^7.2.0", "symfony/asset-mapper": "^7.2.5", "symfony/config": "^7.2.3", "symfony/console": "^7.2.5", "symfony/dotenv": "^7.2.0", "symfony/expression-language": "^7.2.0", "symfony/flex": "^2.5.0", "symfony/form": "^7.2.5", "symfony/framework-bundle": "^7.2.5", "symfony/mime": "^7.2.4", "symfony/monolog-bundle": "^3.10.0", "symfony/runtime": "^7.2.3", "symfony/security-bundle": "^7.2.3", "symfony/twig-bundle": "^7.2.0", "symfony/ux-twig-component": "^2.24.0", "symfony/validator": "^7.2.5", "symfony/yaml": "^7.2.5", "twig/extra-bundle": "^3.20.0", "twig/twig": "^3.20.0", "visymo/antelope-api-client": "^1.0.1", "visymo/command-bus": "^6.1.0", "visymo/config-api-client": "^2.1.0", "visymo/elasticsearch-api-client": "^3.8.0", "visymo/filesystem": "^1.0.0", "visymo/monolog-extensions-bundle": "^8.5.0", "visymo/query-bus": "^1.0.0", "visymo/saml": "^1.0.0", "visymo/sentry-bundle": "^8.0.0", "visymo/serializer": "^1.0.0", "visymo/shared": "^17.0.0"}, "require-dev": {"ext-zip": "*", "escapestudios/symfony2-coding-standard": "^3.15.0", "mockery/mockery": "^1.6.12", "phpstan/phpstan": "^2.1.12", "phpstan/phpstan-deprecation-rules": "^2.0.1", "phpstan/phpstan-mockery": "^2.0.0", "phpstan/phpstan-phpunit": "^2.0.6", "phpstan/phpstan-strict-rules": "^2.0.4", "phpstan/phpstan-symfony": "^2.0.4", "phpunit/phpunit": "^12.1.2", "roave/security-advisories": "dev-latest", "slevomat/coding-standard": "8.15.0", "squizlabs/php_codesniffer": "^3.12.2", "symfony/web-profiler-bundle": "^7.2.4", "visymo/artemis-api-client": "^7.0.0", "visymo/coding-standards": "^10.1.0", "visymo/frontend-coding-standards": "^1.0.2", "visymo/phpunit-extensions": "^1.1.0"}, "config": {"gitlab-domains": ["git.visymo.com"], "bin-dir": "bin", "bin-compat": "full", "allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "dealerdirect/phpcodesniffer-composer-installer": true}, "sort-packages": true, "platform": {"php": "8.3"}}, "autoload": {"psr-4": {"Application\\": "src/Application/", "Domain\\": "src/Domain/", "Framework\\": "src/Framework/", "Infrastructure\\": "src/Infrastructure/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Artemis\\DevelopBundle\\": "bundles/develop-bundle/src", "Artemis\\DevelopBundle\\Tests\\": "bundles/develop-bundle/tests"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts", "@visymo-cs", "@visymo-fcs", "update-supervisor"], "post-update-cmd": ["@auto-scripts", "@visymo-cs", "@visymo-fcs"], "visymo-cs": ["visymo-cs self-update", "visymo-cs auto-scripts"], "visymo-fcs": ["visymo-fcs self-update"], "update-config": ["bin/console develop:brand:download-production"], "import-config": ["bin/console develop:antelope-brand:import"], "update-supervisor": ["bin/console develop:supervisor:generate-config", "update-supervisor"], "reset-db": ["bin/console develop:database:reset"], "reset-db-use-acc": ["bin/console develop:database:reset-with-acceptation"], "diff-db": ["bin/console doctrine:schema:update --dump-sql"], "update-db": ["bin/console doctrine:schema:update --dump-sql --force"], "reset-rabbitmq": ["bin/console develop:rabbitmq:reset"], "assets": ["bin/console asset-map:compile", "yarn dev"], "reset": ["composer install", "yarn", "@assets", "@reset-db", "@reset-rabbitmq"], "reset-use-acc": ["composer install", "yarn", "@assets", "@reset-db-use-acc", "@reset-rabbitmq"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "repositories": [{"type": "composer", "url": "https://git.visymo.com/api/v4/group/6/-/packages/composer/packages.json", "canonical": false}]}
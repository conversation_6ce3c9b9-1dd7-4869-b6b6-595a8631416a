<?php

declare(strict_types=1);

namespace Tests\Integration\Infrastructure\UserInterface\Console\AntelopeBrand;

use Application\Command\Brand\ImportAntelopeBrand\ImportAntelopeBrandCommand;
use Domain\AntelopeBrand\File\AntelopeBrandFileFactory;
use Infrastructure\UserInterface\Console\AntelopeBrand\ImportConfigConsole;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Console\Application;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Tester\CommandTester;
use Symfony\Component\HttpKernel\KernelInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;
use Visymo\Filesystem\File\Exception\FileNotReadableException;
use Visymo\Filesystem\SerializedFile\SerializedFileFactory;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;

class ImportConfigConsoleTest extends AbstractSymfonyIntegrationTest
{
    private string $testJsonFile;

    /** @var array<string, mixed> */
    private array $config;

    public function testSucces(): void
    {
        /** @var AntelopeBrandFileFactory $antelopeBrandFileFactory */
        $antelopeBrandFileFactory = self::getContainer()->get(AntelopeBrandFileFactory::class);

        /** @var SerializedFileFactory $serializedFileFactory */
        $serializedFileFactory = self::getContainer()->get(SerializedFileFactory::class);

        /** @var LoggerInterface $logger */
        $logger = self::getContainer()->get(LoggerInterface::class);

        /** @var CommandBusInterface & MockObject $commandBus */
        $commandBus = $this->createMock(CommandBusInterface::class);
        $importAntelopeBrandCommand = new ImportAntelopeBrandCommand(
            'antelope-brand',
            $this->config,
            false
        );

        $commandBus->expects($this->once())
            ->method('handle')
            ->with($importAntelopeBrandCommand);

        $command = new ImportConfigConsole(
            $antelopeBrandFileFactory,
            $serializedFileFactory,
            $commandBus,
            $logger,
        );

        /** @var KernelInterface $kernel */
        $kernel = self::$kernel;

        $application = new Application($kernel);
        $application->add($command);

        $commandTester = new CommandTester($application->find('app:config:import'));
        $exitCode = $commandTester->execute(
            [
                'config_json_file_path' => $this->testJsonFile,
            ]
        );

        static::assertSame(Command::SUCCESS, $exitCode);
        static::assertStringContainsString('Successfully imported brand config files', $commandTester->getDisplay());
    }

    public function testCouldNotReadFile(): void
    {
        /** @var AntelopeBrandFileFactory $antelopeBrandFileFactory */
        $antelopeBrandFileFactory = self::getContainer()->get(AntelopeBrandFileFactory::class);

        /** @var SerializedFileFactory $serializedFileFactory */
        $serializedFileFactory = self::getContainer()->get(SerializedFileFactory::class);

        /** @var CommandBusInterface $commandBus */
        $commandBus = self::getContainer()->get(CommandBusInterface::class);

        /** @var LoggerInterface & MockObject $logger */
        $logger = $this->createMock(LoggerInterface::class);

        $logger->expects($this->once())
            ->method('critical')
            ->with(
                'Caught {exception} while importing antelope brand config file: {message}',
                [
                    'exception' => FileNotReadableException::class,
                    'message'   => 'Could not read file "non-existing-file.json"',
                    'file_path' => 'non-existing-file.json',
                ]
            );

        $command = new ImportConfigConsole(
            $antelopeBrandFileFactory,
            $serializedFileFactory,
            $commandBus,
            $logger
        );

        /** @var KernelInterface $kernel */
        $kernel = self::$kernel;

        $application = new Application($kernel);
        $application->add($command);

        $commandTester = new CommandTester($application->find('app:config:import'));
        $exitCode = $commandTester->execute(
            [
                'config_json_file_path' => 'non-existing-file.json',
            ]
        );

        static::assertSame(Command::FAILURE, $exitCode);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->config = [
            'data' => 'some test content',
        ];

        $this->testJsonFile = sys_get_temp_dir().'/antelope-brand.json';
        file_put_contents(
            $this->testJsonFile,
            json_encode($this->config, JSON_THROW_ON_ERROR)
        );
    }

    protected function tearDown(): void
    {
        @unlink($this->testJsonFile);

        parent::tearDown();
    }
}

<?php

declare(strict_types=1);

namespace Tests\Integration\Infrastructure\UserInterface\Web\Form\Brand\Validator;

use Domain\Brand\Brand;
use Domain\ContentPage\ContentPage;
use Domain\ContentSearch\ContentSearch;
use Domain\SearchRoute\SearchRoute;
use Infrastructure\UserInterface\Web\Form\Brand\BrandType;
use Infrastructure\UserInterface\Web\Form\Brand\Validator\SearchRouteDependencyFormValidator;
use Infrastructure\UserInterface\Web\Security\Helper\UserHelperInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Form\FormFactory;
use Tests\Stub\Domain\Author\AuthorStubBuilder;
use Tests\Stub\Domain\Brand\BrandStubBuilder;
use Tests\Stub\Domain\User\UserStubBuilder;
use Tests\Stub\Infrastructure\Persistence\Database\DatabaseStub;
use Tests\Stub\Infrastructure\UserInterface\Web\Security\Helper\UserHelperStub;

class SearchRouteDependencyFormValidatorTest extends KernelTestCase
{
    private FormFactory $formFactory;

    private SearchRouteDependencyFormValidator $searchRouteDependencyFormValidator;

    protected function setUp(): void
    {
        parent::setUp();

        $databaseStub = new DatabaseStub(
            self::getContainer()
        );
        $databaseStub->build();

        $userStubBuilder = new UserStubBuilder();
        $userHelperStub = new UserHelperStub();
        $userHelperStub->setUser($userStubBuilder->create());
        self::getContainer()->set(
            UserHelperInterface::class,
            $userHelperStub
        );

        /** @var FormFactory $formFactory */
        $formFactory = self::getContainer()->get('form.factory');
        $this->formFactory = $formFactory;

        /** @var SearchRouteDependencyFormValidator $searchRouteDependencyFormValidator */
        $searchRouteDependencyFormValidator = self::getContainer()->get(
            SearchRouteDependencyFormValidator::class
        );
        $this->searchRouteDependencyFormValidator = $searchRouteDependencyFormValidator;
    }

    /**
     * @return mixed[]
     */
    public static function validateDataProvider(): array
    {
        $brandStubBuilder = new BrandStubBuilder();
        $authorStubBuilder = new AuthorStubBuilder();

        return [
            'valid'   => [
                'brand'              => $brandStubBuilder
                    ->reset()
                    ->setSlug('zapmeta')
                    ->setContentPage(
                        new ContentPage(
                            true,
                            'zapmeta',
                            $authorStubBuilder->create(),
                            true,
                            SearchRoute::CONTENT_SEARCH
                        )
                    )
                    ->setContentSearch(
                        new ContentSearch(true)
                    )
                    ->create(),
                'expectedFormErrors' => false,
            ],
            'invalid' => [
                'brand'              => $brandStubBuilder
                    ->reset()
                    ->setSlug('zapmeta')
                    ->setContentPage(
                        new ContentPage(
                            true,
                            'zapmeta',
                            $authorStubBuilder->create(),
                            true,
                            SearchRoute::CONTENT_SEARCH
                        )
                    )
                    ->setContentSearch(
                        new ContentSearch(false)
                    )
                    ->create(),
                'expectedFormErrors' => true,
            ],
        ];
    }

    #[DataProvider('validateDataProvider')]
    public function testValidate(Brand $brand, bool $expectedFormErrors): void
    {
        $form = $this->formFactory->create(BrandType::class, $brand);

        $validationErrors = $this->searchRouteDependencyFormValidator->validate($form);

        if ($expectedFormErrors) {
            self::assertNotCount(0, $validationErrors);
        } else {
            self::assertCount(0, $validationErrors);
        }
    }
}

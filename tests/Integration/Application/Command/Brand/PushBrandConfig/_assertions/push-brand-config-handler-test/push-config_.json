[{"slug": "seekweb", "config": {"checksum": "f7a2d75a04bdc6ba54a7698582e47437", "brand": {"name": "Seekweb", "partner_slug": null, "slug": "seekweb", "conversion_pixel_url": null, "google_adsense": {"approval": true, "contract_type": "direct", "default_client": "seekweb-web", "default_channel": "seekweb_seo"}, "bing_ads": {"approval": false, "default_ad_unit_id": null}, "active": true, "article": {"enabled": true}, "cheq": {"enabled": true}, "content_page": {"enabled": true, "collection": "seekweb", "author": {"slug": "editorial_team", "name": "editorial_team"}, "use_brand_for_organic_results": false, "organic_result_route": null}, "content_page_home": {"enabled": true, "type": "home_2", "search_route": "route_display_search_related_web"}, "content_search": {"enabled": true}, "display_search": {"enabled": true}, "display_search_related": {"enabled": true, "related_fallback_enabled": true, "style_id_desktop": "1234567890", "style_id_mobile": "1234567890", "web_style_id_desktop": "1234567890", "web_style_id_mobile": "1234567890"}, "google_publisher_tag": {"enabled": true, "ad_unit_path": "googlePublisherTagId"}, "google_tag_manager": {"enabled": true, "google_tag_manager_id": "googleTagManagerId", "routes": ["route_web_search"]}, "image_search": {"enabled": false}, "info_pages": {"link_to_external_about_page": false, "link_to_visymo_publishing": false, "page_type": "search"}, "javascript_related_terms": {"enabled": true, "default_style_id": "1234567890", "content_enabled": true, "content_click_route": "route_display_search_related", "search_enabled": true, "search_click_route": "route_web_search"}, "json_template": {"template_variant": "dark", "template_overrides": ["dsr_ea", "ws_ea"]}, "microsoft_search": {"enabled": true}, "microsoft_search_related": {"enabled": true, "style_id_desktop": "1234567890", "style_id_mobile": "1234567891"}, "monetization": {"ads_enabled": true, "related_terms_enabled": true, "display_banners_enabled": true}, "news_search": {"enabled": false}, "one_trust": {"enabled": true, "domain_script_id": "domainScriptId"}, "pageview_conversion": {"enabled": true, "routes": ["route_display_search_related"]}, "search": {"enabled": true, "seo_enabled": false, "style_id_desktop": "1234567890", "style_id_mobile": "1234567891"}, "spam_click_detect": {"enabled": true, "routes": ["route_display_search_related_web"]}, "tracking": {"campaign_name_validation_enabled": true}, "web_search": {"enabled": true, "style_id_desktop": "1234567890", "style_id_mobile": "1234567891"}}, "domains": {"ae.seekweb.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "en_AE", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}, "ar-int.seekweb.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "ar_<PERSON>", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}, "ar.seekweb.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "es_AR", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}, "at.seekweb.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "de_AT", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}, "au.seekweb.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "en_AU", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}, "us.seekweb.com": {"javascript_related_terms_enabled": false, "locales": [{"locale": "en_US", "is_default": true}], "bing_ads": {"enabled": false}, "google_adsense": {"enabled": true}}}, "redirect_domains": {"www.seekweb.com": {"redirect_strategy": "geo_ip"}}, "accounts": {"12325": {"name": "SW CA 1 (NOK)", "service": "Microsoft Advertising", "campaigns_v2": [{"name": "sw_ca_ba_01"}, {"name": "sw_ca_ba_02"}, {"name": "sw_ca_ba_03"}], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": false}, "google_adsense": {"enabled": true, "sem_client": "seekweb-sml", "web_client": "seekweb-sml"}, "bing_ads": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": ********, "conversion_tracking_label": "clicktrack"}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}, "23144": {"name": "CPA SW CA SRAG 1 (USD)", "service": "Microsoft Advertising", "campaigns_v2": [{"name": "sw_hk_ba_04"}, {"name": "sw_ie_ba_04"}, {"name": "sw_ie_ba_05"}, {"name": "sw_ie_sr_ba_01"}, {"name": "sw_ie_sr_ba_02"}], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": false}, "google_adsense": {"enabled": true, "sem_client": "seekweb-sml", "web_client": "seekweb-sml"}, "bing_ads": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": true, "conversion_tracking_id": 56168151, "conversion_tracking_label": "clickouts"}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}, "29268": {"name": "ROAS SW ZA 1", "service": "Google Ads", "campaigns_v2": [{"name": "sw_smh_camp_137"}, {"name": "sw_smh_camp_138"}, {"name": "sw_smh_camp_139"}, {"name": "sw_smh_camp_14"}, {"name": "sw_smh_camp_140"}, {"name": "sw_smh_camp_141"}, {"name": "sw_smh_camp_142"}, {"name": "sw_smh_camp_143"}, {"name": "sw_smh_camp_144"}, {"name": "sw_smh_camp_145"}, {"name": "sw_smh_camp_146"}, {"name": "sw_smh_camp_147"}, {"name": "sw_smh_camp_148"}, {"name": "sw_smh_camp_149"}, {"name": "sw_smh_camp_15"}, {"name": "sw_smh_camp_150"}, {"name": "sw_smh_camp_151"}, {"name": "sw_smh_camp_152"}, {"name": "sw_smh_camp_153"}, {"name": "sw_smh_camp_154"}], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": true}, "google_adsense": {"enabled": true, "sem_client": "seekweb-smh", "web_client": "seekweb-smh"}, "bing_ads": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}, "35696": {"name": "ROAS SW JP KIA 2 [Mobile GG Only]", "service": "Google Ads", "campaigns_v2": [{"name": "sw_au_gc4_03"}, {"name": "sw_au_gcm_01"}], "payment_mode": null, "conversion_log": {"enabled": true, "offline_conversion": true}, "google_adsense": {"enabled": true, "sem_client": "seekweb-smh", "web_client": "seekweb-smh"}, "bing_ads": {"enabled": false}, "google_ads_conversion_tracking": {"enabled": false}, "microsoft_ads_conversion_tracking": {"enabled": false}, "zemanta_conversion_tracking": {"enabled": false}, "exclude_countries_from_conversion_tracking": []}}, "split_tests": []}}]
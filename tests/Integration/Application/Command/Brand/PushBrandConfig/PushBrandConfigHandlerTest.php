<?php

declare(strict_types=1);

namespace Integration\Application\Command\Brand\PushBrandConfig;

use Application\Command\Brand\PushBrandConfig\PushBrandConfigCommand;
use Application\Command\Brand\PushBrandConfig\PushBrandConfigHandler;
use Domain\BrandConfig\Event\BrandConfigWasPushedEvent;
use Domain\ConfigApi\Client\ConfigApiClientInterface;
use Monolog\Level;
use Psr\Log\LoggerInterface;
use Tests\Stub\Infrastructure\Persistence\Database\DatabaseStub;
use Tests\Stub\Infrastructure\Service\ConfigApi\ConfigApiClientStub;
use Tests\Stub\Vendor\Visymo\Shared\Domain\Event\EventDispatcherStub;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;
use Visymo\Shared\Domain\Event\EventDispatcherInterface;
use Visymo\Shared\Infrastructure\Stub\Domain\Logger\MemoryLoggerStub;

class PushBrandConfigHandlerTest extends AbstractSymfonyIntegrationTest
{
    private ConfigApiClientStub $configApiClientStub;

    private EventDispatcherStub $eventDispatcherStub;

    private MemoryLoggerStub $memoryLoggerStub;

    private PushBrandConfigHandler $pushBrandConfigHandler;

    protected function setUp(): void
    {
        parent::setUp();

        $databaseStub = new DatabaseStub(self::getContainer());
        $databaseStub->build();

        $this->configApiClientStub = new ConfigApiClientStub();
        self::getContainer()->set(
            ConfigApiClientInterface::class,
            $this->configApiClientStub
        );

        $this->eventDispatcherStub = new EventDispatcherStub();
        self::getContainer()->set(
            EventDispatcherInterface::class,
            $this->eventDispatcherStub
        );

        /** @var MemoryLoggerStub $memoryLoggerStub */
        $memoryLoggerStub = self::getContainer()->get(LoggerInterface::class);
        $this->memoryLoggerStub = $memoryLoggerStub;

        /** @var PushBrandConfigHandler $pushBrandConfigHandler */
        $pushBrandConfigHandler = self::getContainer()->get(PushBrandConfigHandler::class);
        $this->pushBrandConfigHandler = $pushBrandConfigHandler;
    }

    public function testUnknownBrand(): void
    {
        $command = new PushBrandConfigCommand('hello');
        $this->pushBrandConfigHandler->handle($command);

        self::assertCount(1, $this->memoryLoggerStub->getNormalizedLogs(Level::Error));
    }

    public function testPushConfig(): void
    {
        $command = new PushBrandConfigCommand('seekweb');
        $this->pushBrandConfigHandler->handle($command);

        self::assertNotEmpty($this->configApiClientStub->updateBrandConfig);
        self::assertTrue($this->eventDispatcherStub->hasDispatchedEvent(BrandConfigWasPushedEvent::NAME));

        $assertionFile = $this->initJsonAssertionFile($this->configApiClientStub->updateBrandConfig);
        $assertionFile->assertSame();
    }
}

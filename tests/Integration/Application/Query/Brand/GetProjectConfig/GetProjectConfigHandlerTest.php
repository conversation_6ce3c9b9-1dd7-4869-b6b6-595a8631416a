<?php

declare(strict_types=1);

namespace Tests\Integration\Application\Query\Brand\GetProjectConfig;

use Application\Query\Brand\GetProjectConfig\GetProjectConfigQuery;
use Application\Query\Brand\GetProjectConfig\GetProjectConfigResponse;
use Tests\Stub\Infrastructure\Persistence\Database\DatabaseStub;
use Visymo\PhpunitExtensions\Symfony\IntegrationTest\AbstractSymfonyIntegrationTest;
use Visymo\QueryBus\QueryBus\QueryBusInterface;

class GetProjectConfigHandlerTest extends AbstractSymfonyIntegrationTest
{
    private QueryBusInterface $queryBus;

    protected function setUp(): void
    {
        parent::setUp();

        $databaseStub = new DatabaseStub(self::getContainer());
        $databaseStub->build();

        /** @var QueryBusInterface $queryBus */
        $queryBus = self::getContainer()->get(QueryBusInterface::class);
        $this->queryBus = $queryBus;
    }

    public function testGetProjectConfig(): void
    {
        /** @var GetProjectConfigResponse $response */
        $response = $this->queryBus->handle(
            new GetProjectConfigQuery()
        );

        $assertionFile = $this->initJsonAssertionFile($response->toArray());
        $assertionFile->assertSame();
    }
}

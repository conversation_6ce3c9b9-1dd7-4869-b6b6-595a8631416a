{"$id": "https://www.visymo.com/test.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "additionalProperties": false, "type": "object", "definitions": {"component": {"anyOf": [{"$ref": "#/definitions/components/columnsComponent"}, {"$ref": "#/definitions/components/columnsRangeComponent"}, {"$ref": "#/definitions/components/contentPageExcerptComponent"}, {"$ref": "#/definitions/components/currentPageMatchesComponent"}, {"$ref": "#/definitions/components/disclaimerComponent"}, {"$ref": "#/definitions/components/titleComponent"}]}, "components": {"columnsComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"layout": {"type": "string", "default": "default", "enum": ["default", "dsrw", "image-search", "info"]}, "main_column": {"type": ["string", "null"], "default": null, "enum": ["one", "three", "two"]}, "one": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "section": {"type": ["string", "null"], "default": null, "pattern": "^([a-z][a-z0-9-]*[a-z0-9])$"}, "section_css_properties": {"type": "array", "default": [], "items": {"type": "string", "enum": ["background", "border-top", "box-shadow", "color"]}}, "section_visible": {"type": "boolean", "default": true}, "three": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "two": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "type": {"type": "string", "const": "columns"}}}, "columnsRangeComponent": {"additionalProperties": false, "type": "object", "required": ["end", "start"], "properties": {"components": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "end": {"type": "integer", "minimum": 1, "maximum": 3}, "layout": {"type": "string", "default": "default", "enum": ["default"]}, "section": {"type": ["string", "null"], "default": null, "pattern": "^([a-z][a-z0-9-]*[a-z0-9])$"}, "section_css_properties": {"type": "array", "default": [], "items": {"type": "string", "enum": ["background", "border-top", "box-shadow", "color"]}}, "section_visible": {"type": "boolean", "default": true}, "start": {"type": "integer", "minimum": 1, "maximum": 3}, "type": {"type": "string", "const": "columns_range"}}}, "contentPageExcerptComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"type": "array", "default": [], "items": {"type": "string", "enum": ["bottom", "bottom-5l", "bottom-l", "bottom-xl", "bottom-xxl", "top", "top-l", "top-xl", "top-xxl"]}}, "layout": {"type": "string", "default": "default", "enum": ["default"]}, "max_length": {"type": ["integer", "null"], "default": null}, "split_on_line_end": {"type": "boolean", "default": false}, "start_after_length": {"type": ["integer", "null"], "default": null}, "type": {"type": "string", "const": "content_page_excerpt"}}}, "currentPageMatchesComponent": {"additionalProperties": false, "type": "object", "required": ["type", "page"], "properties": {"no": {"type": "array", "items": {"$ref": "#/definitions/component"}}, "page": {"type": "integer", "exclusiveMinimum": 0}, "type": {"type": "string", "const": "current_page_matches"}, "yes": {"type": "array", "items": {"$ref": "#/definitions/component"}}}}, "disclaimerComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"type": "array", "default": [], "items": {"type": "string", "enum": ["bottom", "bottom-5l", "bottom-l", "bottom-xl", "bottom-xxl", "top", "top-l", "top-xl", "top-xxl"]}}, "layout": {"type": "string", "default": "default", "enum": ["content", "default", "title"]}, "type": {"type": "string", "const": "disclaimer"}}}, "titleComponent": {"additionalProperties": false, "type": "object", "required": [], "properties": {"component_space_modifiers": {"type": "array", "default": [], "items": {"type": "string", "enum": ["bottom", "bottom-5l", "bottom-l", "bottom-xl", "bottom-xxl", "top", "top-l", "top-xl", "top-xxl"]}}, "layout": {"type": "string", "default": "default", "enum": ["bar", "content-1", "content-2", "content-3", "content-4", "content-5", "default", "display", "dsr", "dsr-dark", "header"]}, "subtitle": {"type": ["string", "null"], "default": null}, "subtitle_translation_id": {"type": ["string", "null"], "default": null}, "title": {"type": ["string", "null"], "default": null}, "title_highlight": {"type": ["string", "null"], "default": null}, "title_highlight_translation_id": {"type": ["string", "null"], "default": null}, "translation_id": {"type": ["string", "null"], "default": null}, "type": {"type": "string", "const": "title"}}}}}, "required": ["components"], "properties": {"components": {"additionalProperties": false, "default": [], "items": {"$ref": "#/definitions/component"}, "type": "array"}, "container": {"type": "object", "default": null, "additionalProperties": false, "properties": {"font": {"type": ["string", "null"], "default": null, "enum": ["Inter"]}, "layout": {"type": "string", "default": "default", "enum": ["article", "async", "content-category", "content-home-1", "content-home-2", "content-home-3", "content-home-4", "content-home-5", "content-home-6", "default", "dsr", "startpage", "web-search-category"]}, "mode": {"type": ["string", "null"], "default": null, "enum": ["dark", "small"]}}}, "description": {"type": ["string", "null"], "default": null}, "layout": {"type": "object", "default": [], "additionalProperties": false, "properties": {"template": {"type": "string", "default": "@theme/layout_default_components.html.twig"}}}}}
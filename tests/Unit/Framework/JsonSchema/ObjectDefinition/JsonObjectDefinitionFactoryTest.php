<?php

declare(strict_types=1);

namespace Tests\Unit\Framework\JsonSchema\ObjectDefinition;

use Framework\JsonSchema\JsonSchemaInterface;
use Framework\JsonSchema\ObjectDefinition\JsonObjectDefinitionFactory;
use Framework\JsonSchema\PropertyType\PropertyTypeFactory;
use Framework\JsonSchema\PropertyType\PropertyTypeInterface;
use Infrastructure\Service\JsonTemplateSchema\JsonTemplateSchema;
use PHPUnit\Framework\Attributes\DataProvider;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

class JsonObjectDefinitionFactoryTest extends PhpUnitTestCase
{
    private JsonSchemaInterface $jsonSchema;

    private JsonObjectDefinitionFactory $jsonObjectDefinitionFactory;

    protected function setUp(): void
    {
        parent::setUp();

        $this->jsonSchema = $this->getJsonSchema();
        $this->jsonObjectDefinitionFactory = new JsonObjectDefinitionFactory(
            new PropertyTypeFactory($this->jsonSchema)
        );
    }

    /**
     * @return mixed[]
     */
    public static function createDataProvider(): array
    {
        return [
            'columns component'              => [
                'pointer' => '#/definitions/components/columnsComponent',
                'options' => [
                    'section' => 'pizza',
                    'one'     => [
                        [
                            'type' => 'disclaimer',
                        ],
                        [
                            'type'   => 'title',
                            'layout' => 'display',
                        ],
                    ],
                ],
            ],
            'current page matches component' => [
                'pointer' => '#/definitions/components/currentPageMatchesComponent',
                'options' => [
                    'type' => 'current_page_matches',
                    'page' => 3,
                    'no'   => [
                        [
                            'type' => 'disclaimer',
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $options
     */
    #[DataProvider('createDataProvider')]
    public function testCreate(string $pointer, array $options): void
    {
        $objectDefinitionSchemaData = $this->jsonSchema->getDataByPointer($pointer) ?? [];
        $jsonObjectDefinition = $this->jsonObjectDefinitionFactory->create(
            schemaData: $objectDefinitionSchemaData,
            options   : $options
        );

        $actualData = array_map(
            fn (PropertyTypeInterface $property) => $this->propertyToArray($property),
            $jsonObjectDefinition->properties
        );

        $assertionFile = $this->initJsonAssertionFile($actualData);
        $assertionFile->assertSame();
    }

    private function getJsonSchema(): JsonSchemaInterface
    {
        $data = (string)file_get_contents(__DIR__.'/schema/test.schema.json');
        $fileMock = $this->createConfiguredMock(
            SerializedFileInterface::class,
            [
                'getContents' => json_decode(
                    $data,
                    true,
                    512,
                    JSON_THROW_ON_ERROR
                ),
            ]
        );

        return new JsonTemplateSchema($fileMock);
    }

    /**
     * @return array<string, mixed>
     */
    private function propertyToArray(PropertyTypeInterface $property): array
    {
        return [
            'name'             => $property->getName(),
            'type'             => $property->getType()->value,
            'default'          => $property->getDefault(),
            'value'            => $property->getValue(),
            'normalized_value' => $property->getNormalizedValue(),
            'is_required'      => $property->isRequired(),
            'is_nullable'      => $property->isNullable(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Framework\JsonSchema\Cursor;

use Framework\JsonSchema\Cursor\JsonCursorFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;

final class JsonCursorFactoryTest extends PhpUnitTestCase
{
    private JsonCursorFactory $jsonCursorFactory;

    private string $json;

    protected function setUp(): void
    {
        parent::setUp();

        $this->jsonCursorFactory = new JsonCursorFactory();

        $this->json = (string)file_get_contents(
            __DIR__.'/data/data.json'
        );
    }

    /**
     * @return array<string, array<int>>
     */
    public static function createDataProvider(): array
    {
        return [
            'first row, first column'  => [
                'cursorRow'    => 0,
                'cursorColumn' => 0,
            ],
            'first row, second column' => [
                'cursorRow'    => 0,
                'cursorColumn' => 1,
            ],
            'container'                => [
                'cursorRow'    => 7,
                'cursorColumn' => 12,
            ],
            'somewhere'                => [
                'cursorRow'    => 178,
                'cursorColumn' => 57,
            ],
            'last row, last column'    => [
                'cursorRow'    => 249,
                'cursorColumn' => 0,
            ],
        ];
    }

    #[DataProvider('createDataProvider')]
    public function testCreate(int $cursorRow, int $cursorColumn): void
    {
        $jsonCursor = $this->jsonCursorFactory->create(
            json        : $this->json,
            cursorRow   : $cursorRow,
            cursorColumn: $cursorColumn
        );

        $assertionFile = $this->initJsonAssertionFile($jsonCursor?->toArray() ?? []);
        $assertionFile->assertSame();
    }
}

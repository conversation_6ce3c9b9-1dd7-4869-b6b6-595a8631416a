<?php

declare(strict_types=1);

namespace Tests\Unit\Application\Command\Brand\ImportAntelopeBrand;

use Application\Command\Brand\ImportAntelopeBrand\ImportAntelopeBrandCommand;
use Application\Command\Brand\ImportAntelopeBrand\ImportAntelopeBrandHandler;
use Domain\AntelopeApi\Client\AntelopeApiClientInterface;
use Domain\AntelopeBrand\AntelopeBrand;
use Domain\AntelopeBrand\AntelopeBrandFactory;
use Domain\AntelopeBrand\AntelopeBrandStatus;
use Domain\AntelopeBrand\Validator\AntelopeBrandValidator;
use Domain\Brand\Brand;
use Domain\Generic\ArrayComparer;
use Domain\Generic\ChecksumGenerator;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Psr\Log\LoggerInterface;
use Tests\Stub\Domain\AntelopeBrand\AntelopeBrandRepositoryStub;
use Tests\Stub\Domain\Brand\BrandRepositoryStub;
use Tests\Stub\Domain\Brand\BrandStubBuilder;
use Tests\Stub\Vendor\Visymo\Shared\Domain\Event\EventDispatcherStub;

class ImportAntelopeBrandHandlerTest extends TestCase
{
    private AntelopeBrandRepositoryStub $antelopeBrandRepositoryStub;

    private LoggerInterface & MockObject $logger;

    private ImportAntelopeBrandHandler $importAntelopeHandler;

    private AntelopeApiClientInterface & MockObject $antelopeApiClient;

    private AntelopeBrandValidator & MockObject $antelopeBrandValidator;

    private BrandRepositoryStub $brandRepositoryStub;

    public function setUp(): void
    {
        $brandStubBuilder = new BrandStubBuilder();

        $brand = $brandStubBuilder->reset()
            ->setSlug('test-slug')
            ->create();
        $brand2 = $brandStubBuilder->reset()
            ->setSlug('test-slug2')
            ->create();

        $this->brandRepositoryStub = new BrandRepositoryStub();
        $this->brandRepositoryStub->storeBrands([$brand, $brand2]);

        $antelopeBrand = new AntelopeBrand(
            [
                'brand' => [
                    'slug'           => 'test-slug',
                    'google_adsense' => [
                        'approval'      => true,
                        'contract_type' => 'online',
                    ],
                ],
            ],
            AntelopeBrandStatus::SUCCESS
        );

        $this->antelopeBrandRepositoryStub = new AntelopeBrandRepositoryStub();
        $this->antelopeBrandRepositoryStub->store($antelopeBrand);

        $this->antelopeBrandValidator = $this->createMock(AntelopeBrandValidator::class);

        $eventDispatcherStub = new EventDispatcherStub();
        $this->antelopeApiClient = $this->createMock(AntelopeApiClientInterface::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->importAntelopeHandler = new ImportAntelopeBrandHandler(
            $this->antelopeBrandValidator,
            $eventDispatcherStub,
            $this->logger,
            $this->antelopeBrandRepositoryStub,
            new AntelopeBrandFactory(),
            $this->brandRepositoryStub,
            new ArrayComparer(new ChecksumGenerator()),
            $this->antelopeApiClient
        );
    }

    public function testImportAntelopeBrandWithoutConfig(): void
    {
        $command = new ImportAntelopeBrandCommand('test-slug', null, false);
        $config = [
            'brand' => [
                'slug'           => 'test-slug',
                'google_adsense' => [
                    'approval'      => false,
                    'contract_type' => 'online',
                ],
            ],
        ];

        $this->antelopeApiClient->expects($this->once())->method('getBrandConfig')
            ->willReturn($config);

        $this->antelopeBrandValidator->method('validateAndReturnStatus')->willReturn(AntelopeBrandStatus::SUCCESS);

        $this->importAntelopeHandler->handle($command);
        $antelopeBrand = $this->antelopeBrandRepositoryStub->findOneBySlug('test-slug');
        static::assertNotNull($antelopeBrand);
        static::assertFalse($antelopeBrand->hasGoogleAdSenseApproval());
    }

    public function testImportAntelopeBrandWithConfig(): void
    {
        $config = [
            'brand' => [
                'slug'           => 'test-slug',
                'google_adsense' => [
                    'approval'      => false,
                    'contract_type' => 'online',
                ],
            ],
        ];
        $command = new ImportAntelopeBrandCommand('test-slug', $config, false);

        $this->antelopeApiClient->expects($this->never())->method('getBrandConfig')
            ->willReturn($config);

        $this->antelopeBrandValidator->method('validateAndReturnStatus')->willReturn(AntelopeBrandStatus::SUCCESS);

        $this->importAntelopeHandler->handle($command);
        $antelopeBrand = $this->antelopeBrandRepositoryStub->findOneBySlug('test-slug');
        static::assertNotNull($antelopeBrand);
        static::assertFalse($antelopeBrand->hasGoogleAdSenseApproval());
    }

    public function testForce(): void
    {
        $command = new ImportAntelopeBrandCommand('test-slug', null, true);
        $config = [
            'brand' => [
                'slug'           => 'test-slug',
                'google_adsense' => [
                    'approval'      => true,
                    'contract_type' => 'online',
                ],
            ],
        ];

        $this->antelopeApiClient->method('getBrandConfig')
            ->willReturn($config);

        $this->antelopeBrandValidator->method('validateAndReturnStatus')->willReturn(AntelopeBrandStatus::SUCCESS);

        $this->logger->expects($this->never())->method('info');
        $this->importAntelopeHandler->handle($command);
    }

    public function testNotBeingUpdated(): void
    {
        $command = new ImportAntelopeBrandCommand('test-slug', null, false);
        $config = [
            'brand' => [
                'slug'           => 'test-slug',
                'google_adsense' => [
                    'approval'      => true,
                    'contract_type' => 'online',
                ],
            ],
        ];

        $this->antelopeApiClient->method('getBrandConfig')
            ->willReturn($config);

        $this->antelopeBrandValidator->method('validateAndReturnStatus')->willReturn(AntelopeBrandStatus::SUCCESS);

        $this->logger->expects($this->once())->method('info');
        $this->importAntelopeHandler->handle($command);
    }

    public function testImportWithNewAntelopeBrand(): void
    {
        $command = new ImportAntelopeBrandCommand('test-slug2', null, false);

        $brand = $this->brandRepositoryStub->findOneBySlug('test-slug2');

        static::assertInstanceOf(Brand::class, $brand);

        $brand->status = AntelopeBrandStatus::MISSING;
        $this->brandRepositoryStub->store($brand);

        $config = [
            'brand' => [
                'slug'           => 'test-slug2',
                'google_adsense' => [
                    'approval'      => false,
                    'contract_type' => 'online',
                ],
            ],
        ];

        $this->antelopeApiClient->method('getBrandConfig')
            ->willReturn($config);

        $this->antelopeBrandValidator->method('validateAndReturnStatus')->willReturn(AntelopeBrandStatus::SUCCESS);
        $this->importAntelopeHandler->handle($command);

        $antelopeBrand = $this->antelopeBrandRepositoryStub->findOneBySlug('test-slug2');
        static::assertNotNull($antelopeBrand);
        static::assertSame($command->slug, $antelopeBrand->getSlug());
    }
}

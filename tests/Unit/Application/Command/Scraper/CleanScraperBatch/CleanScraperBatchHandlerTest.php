<?php

declare(strict_types=1);

namespace Tests\Unit\Application\Command\Scraper\CleanScraperBatch;

use Application\Command\Scraper\CleanScraperBatch\CleanScraperBatchCommand;
use Application\Command\Scraper\CleanScraperBatch\CleanScraperBatchHandler;
use Domain\Scraper\ScraperBatch\ScraperBatch;
use Domain\Scraper\ScraperBatch\ScraperBatchFactory;
use Domain\Scraper\ScraperBatch\ScraperBatchStatus;
use Domain\SearchRoute\SearchRoute;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Domain\Scraper\ScraperBatch\ScraperBatchRepositoryStub;
use Tests\Stub\Domain\Scraper\ScraperStubBuilder;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Framework\Test\Stub\TestStubRandomizer;

final class CleanScraperBatchHandlerTest extends TestCase
{
    private ScraperBatchRepositoryStub $scraperBatchRepository;

    private CleanScraperBatchHandler $cleanScraperBatchHandler;

    private ScraperBatchFactory $scraperBatchFactory;

    private DateTimeFactory $dateTimeFactory;

    protected function setUp(): void
    {
        $this->scraperBatchRepository = new ScraperBatchRepositoryStub();
        $this->dateTimeFactory = new DateTimeFactory();
        $this->scraperBatchFactory = new ScraperBatchFactory($this->dateTimeFactory);
        $this->cleanScraperBatchHandler = new CleanScraperBatchHandler(
            $this->scraperBatchRepository,
            $this->dateTimeFactory
        );
    }

    public function testHandleDeletesOldScraperBatches(): void
    {
        $oldBatch1 = $this->createScraperBatch('-2 months', 1);
        $oldBatch2 = $this->createScraperBatch('-6 weeks', 2);
        $newBatch = $this->createScraperBatch('-2 weeks', 3);

        $this->scraperBatchRepository->addBatches([$oldBatch1, $oldBatch2, $newBatch]);

        $this->cleanScraperBatchHandler->handle(new CleanScraperBatchCommand());

        $remainingBatches = $this->scraperBatchRepository->findAll();
        self::assertCount(1, $remainingBatches);
        self::assertSame($newBatch, reset($remainingBatches));
    }

    public function testHandleWithNoOldBatches(): void
    {
        $newBatch = $this->createScraperBatch('-2 weeks', 1);
        $this->scraperBatchRepository->store($newBatch);

        $this->cleanScraperBatchHandler->handle(new CleanScraperBatchCommand());

        $remainingBatches = $this->scraperBatchRepository->findAll();
        self::assertCount(1, $remainingBatches);
        self::assertSame($newBatch, reset($remainingBatches));
    }

    private function createScraperBatch(string $startedAtModifier, int $id): ScraperBatch
    {
        $scraperBatch = $this->scraperBatchFactory->create(
            runId          : null,
            locale         : 'en_US',
            description    : null,
            templateVariant: null,
            searchRoute    : SearchRoute::DISPLAY_SEARCH_RELATED,
            scraper        : (new ScraperStubBuilder())->create()
        );

        $scraperBatch->id = $id;
        $scraperBatch->status = ScraperBatchStatus::COMPLETED;
        $dateTime = $this->dateTimeFactory->create($startedAtModifier, TimezoneEnum::UTC);
        $scraperBatch->startedAt = $dateTime;
        $scraperBatch->finishedAt = $dateTime;
        $scraperBatch->executionCount = TestStubRandomizer::createInt(1, 5);

        return $scraperBatch;
    }
}

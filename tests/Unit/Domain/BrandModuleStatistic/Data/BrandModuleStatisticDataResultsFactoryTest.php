<?php

declare(strict_types=1);

namespace Tests\Unit\Domain\BrandModuleStatistic\Data;

use Domain\AccessLog\BrandAccessLogPathResult;
use Domain\BrandModuleStatistic\Data\BrandModuleStatisticDataResultsFactory;
use PHPUnit\Framework\Attributes\DataProvider;
use Visymo\PhpunitExtensions\PhpUnit\TestCase\PhpUnitTestCase;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

class BrandModuleStatisticDataResultsFactoryTest extends PhpUnitTestCase
{
    /**
     * @return mixed[]
     */
    public static function createFromDataDataProvider(): array
    {
        return [
            'empty'     => [
                'data' => [],
            ],
            'with data' => [
                'data' => [
                    'adbot'   => [
                        '/path_one'             => 432,
                        '/path_with_wildcard/*' => 53434,
                    ],
                    'visitor' => [
                        '/path_second' => 9944,
                    ],
                ],
            ],
        ];
    }

    /**
     * @param mixed[] $data
     */
    #[DataProvider('createFromDataDataProvider')]
    public function testCreateFromData(array $data): void
    {
        $brandModuleStatisticResults = BrandModuleStatisticDataResultsFactory::createFromData($data);

        $assertionFile = $this->initJsonAssertionFile($brandModuleStatisticResults->toArray());
        $assertionFile->assertSame();
    }

    /**
     * @return mixed[]
     */
    public static function createFromBrandAccessLogPathResultsDataProvider(): array
    {
        $date = new \DateTime('2024-11-22 01:02:03', TimezoneEnum::UTC->toDateTimeZone());

        return [
            'empty'     => [
                'brandAccessLogPathResults' => [],
            ],
            'with data' => [
                'brandAccessLogPathResults' => [
                    new BrandAccessLogPathResult(
                        slug        : 'seekweb',
                        date        : $date,
                        isAdBot     : true,
                        wildcardPath: '/path_one',
                        count       : 432
                    ),
                    new BrandAccessLogPathResult(
                        slug        : 'seekweb',
                        date        : $date,
                        isAdBot     : false,
                        wildcardPath: '/path_two',
                        count       : 43939
                    ),
                    new BrandAccessLogPathResult(
                        slug        : 'seekweb',
                        date        : $date,
                        isAdBot     : false,
                        wildcardPath: '/path_with_no_count',
                        count       : 0
                    ),
                    new BrandAccessLogPathResult(
                        slug        : 'seekweb',
                        date        : $date,
                        isAdBot     : true,
                        wildcardPath: '/path_with_wildcard/*',
                        count       : 939393
                    ),
                ],
            ],
        ];
    }

    /**
     * @param BrandAccessLogPathResult[] $brandAccessLogPathResults
     */
    #[DataProvider('createFromBrandAccessLogPathResultsDataProvider')]
    public function testCreateFromBrandAccessLogPathResults(array $brandAccessLogPathResults): void
    {
        $brandModuleStatisticDataResultsFactory = new BrandModuleStatisticDataResultsFactory();
        $brandModuleStatisticResults = $brandModuleStatisticDataResultsFactory
            ->createFromBrandAccessLogPathResults(
                new \ArrayIterator($brandAccessLogPathResults)
            );

        $assertionFile = $this->initJsonAssertionFile($brandModuleStatisticResults->toArray());
        $assertionFile->assertSame();
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Domain\UrlScan;

use Domain\UrlScan\Exception\InvalidUrlException;
use Domain\UrlScan\UrlScanNormalizer;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class UrlScanNormalizerTest extends TestCase
{
    /**
     * @return mixed[]
     */
    public static function normalizeUrlDataProvider(): array
    {
        return [
            'valid url'            => [
                'url'               => 'https://example.com/path/search?query=string%20with%20spaces',
                'expectedUrl'       => 'https://example.com/path/search?query=string%20with%20spaces',
                'expectedException' => null,
            ],
            'valid url, lowercase' => [
                'url'               => 'HtTPs://exAMPle.com',
                'expectedUrl'       => 'https://example.com',
                'expectedException' => null,
            ],
            'valid url, with hash' => [
                'url'               => 'https://example.com/path/search?query=string%20with%20spaces#hash',
                'expectedUrl'       => 'https://example.com/path/search?query=string%20with%20spaces#hash',
                'expectedException' => null,
            ],
            'missing schema'       => [
                'url'               => 'example.com/path?query=string',
                'expectedUrl'       => null,
                'expectedException' => InvalidUrlException::class,
            ],
            'missing host'         => [
                'url'               => 'https:///path?query=string',
                'expectedUrl'       => null,
                'expectedException' => InvalidUrlException::class,
            ],
        ];
    }

    /**
     * @param class-string<\Throwable>|null $expectedException
     */
    #[DataProvider('normalizeUrlDataProvider')]
    public function testNormalizeUrl(string $url, ?string $expectedUrl, ?string $expectedException): void
    {
        $urlScanNormalizer = new UrlScanNormalizer();

        if ($expectedException !== null) {
            $this->expectException($expectedException);
        }

        $actualUrl = $urlScanNormalizer->normalizeUrl($url);

        self::assertEquals($expectedUrl, $actualUrl);
    }
}

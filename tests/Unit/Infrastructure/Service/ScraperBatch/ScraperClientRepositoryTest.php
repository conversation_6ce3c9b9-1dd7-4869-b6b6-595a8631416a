<?php

declare(strict_types=1);

namespace Tests\Unit\Infrastructure\Service\ScraperBatch;

use Domain\Scraper\Exception\ScraperClientNotFoundException;
use Domain\Scraper\Scraper;
use Domain\Scraper\ScraperClientInterface;
use Infrastructure\Service\ScraperBatch\ScraperClientRepository;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Domain\Scraper\ScraperStubBuilder;

class ScraperClientRepositoryTest extends TestCase
{
    /**
     * @return array<string, array{scraper: Scraper, clientName: string, shouldMatch: bool}>
     */
    public static function createForScraperDataProvider(): array
    {
        return [
            'valid scraper'   => [
                'scraper'     => new Scraper('apify', 'Apify Scraper', true),
                'clientName'  => 'apify',
                'shouldMatch' => true,
            ],
            'invalid scraper' => [
                'scraper'     => new Scraper('invalid-scraper', 'Invalid Scraper', false),
                'clientName'  => 'apify',
                'shouldMatch' => false,
            ],
        ];
    }

    public function testCreateForScraperWithValidScraper(): void
    {
        $mockClient = $this->createMock(ScraperClientInterface::class);
        $mockClient->method('getName')->willReturn('apify');

        $factory = new ScraperClientRepository([$mockClient]);
        $scraper = (new ScraperStubBuilder())->create();

        $result = $factory->getClientForScraper($scraper);

        self::assertSame($mockClient, $result);
    }

    public function testCreateForScraperWithInvalidScraper(): void
    {
        $mockClient = $this->createMock(ScraperClientInterface::class);
        $mockClient->method('getName')->willReturn('apify');

        $factory = new ScraperClientRepository([$mockClient]);
        $scraper = new Scraper('invalid-scraper', 'Invalid Scraper', false);

        $this->expectException(ScraperClientNotFoundException::class);
        $this->expectExceptionMessage('Could not find scraper client for slug: invalid-scraper');

        $factory->getClientForScraper($scraper);
    }

    #[DataProvider('createForScraperDataProvider')]
    public function testCreateForScraperWithDataProvider(Scraper $scraper, string $clientName, bool $shouldMatch): void
    {
        $mockClient = $this->createMock(ScraperClientInterface::class);
        $mockClient->method('getName')->willReturn($clientName);

        $factory = new ScraperClientRepository([$mockClient]);

        if ($shouldMatch) {
            $result = $factory->getClientForScraper($scraper);
            self::assertSame($mockClient, $result);
        } else {
            $this->expectException(ScraperClientNotFoundException::class);
            $factory->getClientForScraper($scraper);
        }
    }

    public function testMultipleClients(): void
    {
        $mockClient1 = $this->createMock(ScraperClientInterface::class);
        $mockClient1->method('getName')->willReturn('scraper1');

        $mockClient2 = $this->createMock(ScraperClientInterface::class);
        $mockClient2->method('getName')->willReturn('scraper2');

        $factory = new ScraperClientRepository([$mockClient1, $mockClient2]);

        $scraper1 = new Scraper('scraper1', 'Scraper 1', false);
        $result1 = $factory->getClientForScraper($scraper1);
        self::assertSame($mockClient1, $result1);

        $scraper2 = new Scraper('scraper2', 'Scraper 2', false);
        $result2 = $factory->getClientForScraper($scraper2);
        self::assertSame($mockClient2, $result2);
    }
}

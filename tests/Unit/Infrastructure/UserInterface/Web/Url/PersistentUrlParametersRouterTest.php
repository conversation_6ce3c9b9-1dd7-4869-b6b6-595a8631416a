<?php

declare(strict_types=1);

namespace Tests\Unit\Infrastructure\UserInterface\Web\Url;

use Infrastructure\UserInterface\Web\Helper\DevelopHostHelper;
use Infrastructure\UserInterface\Web\Url\PersistentUrlParametersHelper;
use Infrastructure\UserInterface\Web\Url\PersistentUrlParametersRouter;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

class PersistentUrlParametersRouterTest extends TestCase
{
    private RouterInterface & MockObject $router;

    private PersistentUrlParametersHelper & MockObject $persistentUrlParametersHelper;

    private PersistentUrlParametersRouter $persistentUrlParametersRouter;

    protected function setUp(): void
    {
        $this->router = $this->createMock(RouterInterface::class);
        $this->persistentUrlParametersHelper = $this->createMock(PersistentUrlParametersHelper::class);

        $this->persistentUrlParametersRouter = new PersistentUrlParametersRouter(
            $this->router,
            new DevelopHostHelper(),
            $this->persistentUrlParametersHelper
        );
    }

    /**
     * @return mixed[]
     */
    public static function generateDataProvider(): array
    {
        return [
            'no persistent parameters'   => [
                'route'                 => 'route_name',
                'routeParameters'       => ['param1' => 'value1'],
                'absoluteUrl'           => false,
                'persistentParameters'  => [],
                'expectedParameters'    => ['param1' => 'value1'],
                'expectedReferenceType' => UrlGeneratorInterface::ABSOLUTE_PATH,
                'generatedUrl'          => '/path',
            ],
            'with persistent parameters' => [
                'route'                 => 'route_name',
                'routeParameters'       => ['param1' => 'value1'],
                'absoluteUrl'           => false,
                'persistentParameters'  => ['persistent1' => 'value1', 'persistent2' => 'value2'],
                'expectedParameters'    => ['persistent1' => 'value1', 'persistent2' => 'value2', 'param1' => 'value1'],
                'expectedReferenceType' => UrlGeneratorInterface::ABSOLUTE_PATH,
                'generatedUrl'          => '/path?persistent1=value1&persistent2=value2&param1=value1',
            ],
            'with absolute url'          => [
                'route'                 => 'route_name',
                'routeParameters'       => ['param1' => 'value1'],
                'absoluteUrl'           => true,
                'persistentParameters'  => ['persistent1' => 'value1'],
                'expectedParameters'    => ['persistent1' => 'value1', 'param1' => 'value1'],
                'expectedReferenceType' => UrlGeneratorInterface::ABSOLUTE_URL,
                'generatedUrl'          => 'https://example.com/path?persistent1=value1&param1=value1',
            ],
            'with different page type'   => [
                'route'                 => 'route_name',
                'routeParameters'       => ['param1' => 'value1'],
                'absoluteUrl'           => false,
                'persistentParameters'  => ['persistent1' => 'value1'],
                'expectedParameters'    => ['persistent1' => 'value1', 'param1' => 'value1'],
                'expectedReferenceType' => UrlGeneratorInterface::ABSOLUTE_PATH,
                'generatedUrl'          => '/path?persistent1=value1&param1=value1',
            ],
        ];
    }

    /**
     * @return mixed[]
     */
    public static function generateForDomainDataProvider(): array
    {
        return [
            'basic' => [
                'domain'          => 'example.com',
                'route'           => 'route_name',
                'routeParameters' => ['param1' => 'value1'],
                'generatedPath'   => '/path?param1=value1',
                'developDomain'   => 'pizza',
                'expectedUrl'     => 'https://example.com.pizza.ldev.nl/path?param1=value1',
            ],
        ];
    }

    /**
     * @return mixed[]
     */
    public static function redirectToRouteDataProvider(): array
    {
        return [
            'basic' => [
                'route'                => 'route_name',
                'routeParameters'      => ['param1' => 'value1'],
                'status'               => 302,
                'persistentParameters' => ['persistent1' => 'value1'],
                'expectedParameters'   => ['persistent1' => 'value1', 'param1' => 'value1'],
                'generatedUrl'         => 'https://example.com/path?persistent1=value1&param1=value1',
            ],
        ];
    }

    /**
     * @param array<string, string> $routeParameters
     * @param array<string, string> $persistentParameters
     * @param array<string, string> $expectedParameters
     */
    #[DataProvider('generateDataProvider')]
    public function testGenerate(
        string $route,
        array $routeParameters,
        bool $absoluteUrl,
        array $persistentParameters,
        array $expectedParameters,
        int $expectedReferenceType,
        string $generatedUrl
    ): void
    {
        $this->persistentUrlParametersHelper
            ->expects(self::once())
            ->method('getPersistentParameters')
            ->willReturn($persistentParameters);

        $this->router
            ->expects(self::once())
            ->method('generate')
            ->with($route, $expectedParameters, $expectedReferenceType)
            ->willReturn($generatedUrl);

        $result = $this->persistentUrlParametersRouter->generate($route, $routeParameters, $absoluteUrl);

        self::assertSame($generatedUrl, $result);
    }

    /**
     * @param array<string, string> $routeParameters
     */
    #[DataProvider('generateForDomainDataProvider')]
    public function testGenerateForDomain(
        string $domain,
        string $route,
        array $routeParameters,
        string $generatedPath,
        string $developDomain,
        string $expectedUrl
    ): void
    {
        $developHostHelper = new DevelopHostHelper($developDomain);
        $this->persistentUrlParametersRouter = $this->getMockBuilder(PersistentUrlParametersRouter::class)
            ->setConstructorArgs(
                [
                    $this->router,
                    $developHostHelper,
                    $this->persistentUrlParametersHelper,
                ]
            )
            ->onlyMethods(['generate'])
            ->getMock();

        $this->persistentUrlParametersRouter
            ->expects(self::once())
            ->method('generate')
            ->with($route, $routeParameters)
            ->willReturn($generatedPath);

        $result = $this->persistentUrlParametersRouter->generateForDomain($domain, $route, $routeParameters);

        self::assertSame($expectedUrl, $result);
    }

    /**
     * @param array<string, string> $routeParameters
     * @param array<string, string> $persistentParameters
     * @param array<string, string> $expectedParameters
     */
    #[DataProvider('redirectToRouteDataProvider')]
    public function testRedirectToRoute(
        string $route,
        array $routeParameters,
        int $status,
        array $persistentParameters,
        array $expectedParameters,
        string $generatedUrl
    ): void
    {
        $this->persistentUrlParametersHelper
            ->expects(self::once())
            ->method('getPersistentParameters')
            ->willReturn($persistentParameters);

        $this->router
            ->expects(self::once())
            ->method('generate')
            ->with($route, $expectedParameters, UrlGeneratorInterface::ABSOLUTE_URL)
            ->willReturn($generatedUrl);

        $result = $this->persistentUrlParametersRouter->redirectToRoute($route, $routeParameters, $status);

        self::assertSame($generatedUrl, $result->getTargetUrl());
        self::assertSame($status, $result->getStatusCode());
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Infrastructure\UserInterface\Web\Url;

use Infrastructure\UserInterface\Web\Request\Manager\RequestManagerInterface;
use Infrastructure\UserInterface\Web\Request\Normalizer\RequestPropertyNormalizerInterface;
use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestParameterBagInterface;
use Infrastructure\UserInterface\Web\Request\SortRequest;
use Infrastructure\UserInterface\Web\Url\PersistentSortParametersProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PersistentSortParametersProviderTest extends TestCase
{
    /**
     * @return array<string, array<string, array<string, string|null>|string|null>>
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        return [
            'empty sort'                        => [
                'sort'                  => null,
                'sortDirection'         => SortRequest::SORT_DIRECTION_DESC,
                'expectedUrlParameters' => [
                    SortRequest::PARAMETER_SORT           => '',
                    SortRequest::PARAMETER_SORT_DIRECTION => SortRequest::SORT_DIRECTION_DESC,
                ],
            ],
            'with sort'                         => [
                'sort'                  => 'name',
                'sortDirection'         => SortRequest::SORT_DIRECTION_DESC,
                'expectedUrlParameters' => [
                    SortRequest::PARAMETER_SORT           => 'name',
                    SortRequest::PARAMETER_SORT_DIRECTION => SortRequest::SORT_DIRECTION_DESC,
                ],
            ],
            'with sort and different direction' => [
                'sort'                  => 'date',
                'sortDirection'         => SortRequest::SORT_DIRECTION_ASC,
                'expectedUrlParameters' => [
                    SortRequest::PARAMETER_SORT           => 'date',
                    SortRequest::PARAMETER_SORT_DIRECTION => SortRequest::SORT_DIRECTION_ASC,
                ],
            ],
        ];
    }

    /**
     * @param array<string, string> $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(
        ?string $sort,
        string $sortDirection,
        array $expectedUrlParameters
    ): void
    {
        $queryBag = $this->createMock(RequestParameterBagInterface::class);

        if ($sort !== null) {
            $queryBag->method('getString')
                ->with(SortRequest::PARAMETER_SORT)
                ->willReturn($sort);
        }

        $queryBag->method('getAcceptedString')
            ->with(SortRequest::PARAMETER_SORT_DIRECTION, [SortRequest::SORT_DIRECTION_ASC, SortRequest::SORT_DIRECTION_DESC])
            ->willReturn($sortDirection);

        $requestManager = $this->createMock(RequestManagerInterface::class);
        $requestManager->method('queryBag')
            ->willReturn($queryBag);

        $requestPropertyNormalizer = $this->createMock(RequestPropertyNormalizerInterface::class);
        $requestPropertyNormalizer->method('getString')
            ->willReturnCallback(static fn (?string $value): ?string => $value);

        $sortRequest = new SortRequest($requestManager, $requestPropertyNormalizer);

        $provider = new PersistentSortParametersProvider($sortRequest);

        self::assertSame(
            $expectedUrlParameters,
            $provider->getPersistentUrlParameters(),
        );
    }
}

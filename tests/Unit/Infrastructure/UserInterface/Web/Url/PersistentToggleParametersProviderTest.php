<?php

declare(strict_types=1);

namespace Tests\Unit\Infrastructure\UserInterface\Web\Url;

use Infrastructure\UserInterface\Web\Request\Manager\RequestManagerInterface;
use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestParameterBagInterface;
use Infrastructure\UserInterface\Web\Request\ToggleRequest;
use Infrastructure\UserInterface\Web\Url\PersistentToggleParametersProvider;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class PersistentToggleParametersProviderTest extends TestCase
{
    /**
     * @return array<string, array<string, array<string, string>|bool>>
     */
    public static function getPersistentUrlParametersDataProvider(): array
    {
        return [
            'toggle inactive' => [
                'toggleActive'          => false,
                'expectedUrlParameters' => [],
            ],
            'toggle active'   => [
                'toggleActive'          => true,
                'expectedUrlParameters' => [
                    ToggleRequest::PARAMETER_TOGGLE => '1',
                ],
            ],
        ];
    }

    /**
     * @param array<string, string> $expectedUrlParameters
     */
    #[DataProvider('getPersistentUrlParametersDataProvider')]
    public function testGetPersistentUrlParameters(
        bool $toggleActive,
        array $expectedUrlParameters
    ): void
    {
        $queryBag = $this->createMock(RequestParameterBagInterface::class);
        $queryBag->method('getNullableBool')
            ->with(ToggleRequest::PARAMETER_TOGGLE)
            ->willReturn($toggleActive ? true : null);

        $requestManager = $this->createMock(RequestManagerInterface::class);
        $requestManager->method('queryBag')
            ->willReturn($queryBag);

        $toggleRequest = new ToggleRequest($requestManager);

        $provider = new PersistentToggleParametersProvider($toggleRequest);

        self::assertSame(
            $expectedUrlParameters,
            $provider->getPersistentUrlParameters(),
        );
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\AntelopeApi\Client;

use Domain\AntelopeApi\Client\AntelopeApiClientInterface;

final class AntelopeApiClientStub implements AntelopeApiClientInterface
{
    /**
     * @param array<string, mixed[]> $brandConfigs
     * @param array<string, int[]>   $brandAdStyles
     */
    public function __construct(
        private readonly array $brandConfigs = [],
        private array $brandAdStyles = []
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getBrandConfig(string $slug): array
    {
        return $this->brandConfigs[$slug] ?? [];
    }

    /**
     * @inheritDoc
     */
    public function pushBrandAdStyles(string $slug, array $styleIds): void
    {
        $this->brandAdStyles[$slug] = $styleIds;
    }

    /**
     * @return array<string, array<int>>
     */
    public function getPushedBrandAdStyles(): array
    {
        return $this->brandAdStyles;
    }
}

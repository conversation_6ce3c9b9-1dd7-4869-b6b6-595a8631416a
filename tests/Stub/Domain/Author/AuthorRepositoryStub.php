<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Author;

use Domain\Author\Author;
use Domain\Author\AuthorRepositoryInterface;
use Domain\Author\Exception\AuthorAlreadyExistsException;

readonly class AuthorRepositoryStub implements AuthorRepositoryInterface
{
    /** @var \ArrayObject<string, Author> */
    private \ArrayObject $authors;

    public function __construct()
    {
        $this->authors = new \ArrayObject();
    }

    public function findAll(): \Generator
    {
        foreach ($this->authors as $author) {
            yield $author;
        }
    }

    public function findOneBySlug(string $slug): ?Author
    {
        return $this->authors->offsetExists($slug)
            ? $this->authors->offsetGet($slug)
            : null;
    }

    public function store(Author $author): void
    {
        if ($this->authors->offsetExists($author->slug)) {
            throw AuthorAlreadyExistsException::create($author->slug);
        }

        $this->authors->offsetSet($author->slug, $author);
    }

    public function delete(Author $author): void
    {
        $this->authors->offsetUnset($author->slug);
    }
}

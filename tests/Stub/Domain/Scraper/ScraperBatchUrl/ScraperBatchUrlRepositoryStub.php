<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Scraper\ScraperBatchUrl;

use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrl;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlRepositoryInterface;

final class ScraperBatchUrlRepositoryStub implements ScraperBatchUrlRepositoryInterface
{
    /** @var \ArrayObject<int, ScraperBatchUrl> */
    private \ArrayObject $scraperBatchUrls;

    public function __construct()
    {
        $this->scraperBatchUrls = new \ArrayObject();
    }

    public function findByScraperBatchId(int $scraperBatchId): \Generator
    {
        foreach ($this->scraperBatchUrls as $scraperBatchUrl) {
            if ($scraperBatchUrl->scraperBatch->id === $scraperBatchId) {
                yield $scraperBatchUrl;
            }
        }
    }

    public function findByScraperBatchIdAndRelatedShown(int $scraperBatchId, bool $relatedShown): \Generator
    {
        foreach ($this->findByScraperBatchId($scraperBatchId) as $scraperBatchUrl) {
            if ($scraperBatchUrl->isRelatedShown === $relatedShown) {
                yield $scraperBatchUrl;
            }
        }
    }

    public function countByScraperBatchIdAndRelatedShown(int $scraperBatchId, ?bool $relatedShown = null): int
    {
        $count = 0;

        foreach ($this->findByScraperBatchId($scraperBatchId) as $scraperBatchUrl) {
            if ($relatedShown === null || $scraperBatchUrl->isRelatedShown === $relatedShown) {
                $count++;
            }
        }

        return $count;
    }

    public function updateIsRelatedShownForBatch(int $scraperBatchId, bool $isRelatedShown): void
    {
        foreach ($this->findByScraperBatchId($scraperBatchId) as $scraperBatchUrl) {
            $scraperBatchUrl->isRelatedShown = $isRelatedShown;
            $this->scraperBatchUrls->offsetSet($scraperBatchUrl->id, $scraperBatchUrl);
        }
    }

    public function store(ScraperBatchUrl $scraperBatchUrl): void
    {
        if (!isset($scraperBatchUrl->id)) {
            /** @var int[] $ids */
            $ids = array_keys($this->scraperBatchUrls->getArrayCopy());
            $scraperBatchUrl->id = $ids === [] ? 1 : max($ids) + 1;
        }

        $this->scraperBatchUrls->offsetSet($scraperBatchUrl->id, $scraperBatchUrl);
    }

    /**
     * @inheritDoc
     */
    public function storeAll(array $scraperBatchUrls): void
    {
        foreach ($scraperBatchUrls as $scraperBatchUrl) {
            $this->store($scraperBatchUrl);
        }
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Scraper;

use Domain\Scraper\Scraper;
use Domain\Scraper\ScraperRepositoryInterface;

readonly class ScraperRepositoryStub implements ScraperRepositoryInterface
{
    /** @var \ArrayObject<int, Scraper> */
    private \ArrayObject $scrapers;

    public function __construct()
    {
        $this->scrapers = new \ArrayObject();
    }

    public function findDefault(): ?Scraper
    {
        foreach ($this->findAll() as $scraper) {
            if ($scraper->isDefault) {
                return $scraper;
            }
        }

        return null;
    }

    public function findOneBySlug(string $slug): ?Scraper
    {
        return $this->scrapers->offsetGet($slug);
    }

    /**
     * @return Scraper[]
     */
    public function findAll(): array
    {
        return $this->scrapers->getArrayCopy();
    }

    public function store(Scraper $scraper): void
    {
        $this->scrapers->offsetSet($scraper->slug, $scraper);
    }
}

<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\BrandAssets;

use Domain\BrandAssets\BrandAssets;
use Domain\BrandAssets\BrandAssetsRepositoryInterface;
use Domain\BrandAssets\Exception\BrandAssetsNotFoundException;

readonly class BrandAssetsRepositoryStub implements BrandAssetsRepositoryInterface
{
    /** @var \ArrayObject<string, BrandAssets> */
    private \ArrayObject $brandAssets;

    public function __construct()
    {
        $this->brandAssets = new \ArrayObject();
    }

    public function findOneBySlug(string $slug): BrandAssets
    {
        /** @var BrandAssets|null $brandAssets */
        $brandAssets = $this->brandAssets->offsetExists($slug)
            ? $this->brandAssets->offsetGet($slug)
            : null;

        if ($brandAssets !== null) {
            return $brandAssets;
        }

        throw BrandAssetsNotFoundException::createForSlug($slug);
    }

    public function findAll(): \Generator
    {
        foreach ($this->brandAssets->getIterator() as $brandAssets) {
            yield $brandAssets;
        }
    }

    public function store(BrandAssets $brandAssets): void
    {
        $this->brandAssets->offsetSet($brandAssets->brand->slug, $brandAssets);
    }
}

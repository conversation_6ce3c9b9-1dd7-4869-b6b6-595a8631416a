<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/12.0/phpunit.xsd"
         backupGlobals="false"
         colors="true"
         bootstrap="phpunit-bootstrap.php"
         defaultTestSuite="all"
         cacheDirectory=".phpunit.cache"
         displayDetailsOnTestsThatTriggerDeprecations="true"
         displayDetailsOnTestsThatTriggerErrors="true"
         displayDetailsOnTestsThatTriggerNotices="true"
         displayDetailsOnTestsThatTriggerWarnings="true"
>
    <php>
        <ini name="error_reporting" value="-1"/>
        <server name="APP_ENV" value="test" force="true"/>
        <server name="SHELL_VERBOSITY" value="-1"/>
        <server name="KERNEL_CLASS" value="Infrastructure\UserInterface\ApplicationKernel"/>
    </php>

    <testsuites>
        <testsuite name="all">
            <directory>tests/Unit</directory>
            <directory>tests/Integration</directory>
            <directory>bundles/develop-bundle/tests/Integration</directory>
        </testsuite>
        <testsuite name="integration">
            <directory>tests/Integration</directory>
            <directory>bundles/develop-bundle/tests/Integration</directory>
        </testsuite>
        <testsuite name="unit">
            <directory>tests/Unit</directory>
        </testsuite>
    </testsuites>

    <source ignoreIndirectDeprecations="false"
            restrictNotices="false"
            restrictWarnings="false">
        <include>
            <directory suffix=".php">src</directory>
        </include>
    </source>
</phpunit>

-   name: Deploy <PERSON> to production
    gather_facts: false
    hosts: artemis_web:artemis_worker
    serial: 4

    vars_files:
        - vars.yaml

    tasks:
        -   name: Create directory with version number
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/directories
                tasks_from: create
        -   name: Extract archive into previously created directory
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/tar
                tasks_from: extract
        -   name: Template .env and dump it
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/env
        -   name: Check platform requirements
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/composer
                tasks_from: check
        -   name: Create tmpfs mount for Symfony var
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/directories
                tasks_from: tmpfs
        -   name: Warm Symfony cache
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/symfony

-   name: Switch symlink to new directory
    gather_facts: false
    hosts: artemis_web
    serial: 1

    vars_files:
        - vars.yaml

    pre_tasks:
        -   name: Disable the server in HAProxy
            ansible.builtin.import_role:
                name: haproxy
                tasks_from: disable_server

    tasks:
        -   name: Switch symbolic link
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/directories
                tasks_from: symlink
        -   name: Restart processes
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/process
        -   name: Clean up old directories
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/directories
                tasks_from: cleanup

    post_tasks:
        -   name: Enable the server in HAProxy
            ansible.builtin.import_role:
                name: haproxy
                tasks_from: enable_server
        -   name: Log release
            ansible.builtin.include_role:
                name: ansible-build-scripts/roles/symfony
                tasks_from: commands
            vars:
                symfony_command_run_type: prod_release

-   name: Switch symlink to new directory for worker nodes
    gather_facts: false
    hosts: artemis_worker

    vars_files:
        - vars.yaml

    tasks:
        -   name: Switch symbolic link
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/directories
                tasks_from: symlink
        -   name: Restart processes
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/process
        -   name: Clean up old directories
            ansible.builtin.import_role:
                name: ansible-build-scripts/roles/directories
                tasks_from: cleanup

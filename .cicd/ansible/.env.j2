###> symfony/framework-bundle ###
APP_ENV={{ lookup('env', 'APP_ENV') }}
APP_SECRET={{ lookup('env', 'APP_SECRET') }}
TRUSTED_PROXIES={{ lookup('env', 'TRUSTED_PROXIES') }}
###< symfony/framework-bundle ###

###> PHP FPM ###
PHP_FPM_SOCKET_PATH={{ lookup('env', 'ARTEMIS_PHP_FPM_SOCKET_PATH') }}
###< PHP FPM ###

###> Logging ###
SENTRY_PROJECT='{{ lookup("env", "ARTEMIS_SENTRY_PROJECT") }}'
SENTRY_DSN={{ lookup('env', 'ARTEMIS_SENTRY_DSN') }}
SENTRY_AUTH_TOKEN='{{ lookup("env", "ARTEMIS_SENTRY_AUTH_TOKEN") }}'
FILEBEAT_URL={{ lookup('env', 'ARTEMIS_FILEBEAT_URL') }}
###< Logging ###

###> Database ###
DATABASE_NAME={{ lookup('env', 'ARTEMIS_DATABASE_NAME') }}
DATABASE_VERSION={{ lookup('env', 'ARTEMIS_DATABASE_VERSION') }}
DATABASE_USER={{ lookup('env', 'ARTEMIS_DATABASE_USER') }}
DATABASE_PASSWORD={{ lookup('env', 'ARTEMIS_DATABASE_PASSWORD') }}
DATABASE_HOST={{ lookup('env', 'ARTEMIS_DATABASE_HOST') }}
DATABASE_PORT={{ lookup('env', 'ARTEMIS_DATABASE_PORT') }}
###< Database ###

### Antelope URL // Enkel benodigd voor develop
ANTELOPE_URL={{ lookup('env', 'ARTEMIS_ANTELOPE_URL') }}
ANTELOPE_API_AUTH_TOKEN={{ lookup('env', 'ARTEMIS_ANTELOPE_API_AUTH_TOKEN') }}

### Artemis API URL (for develop only) ###
ARTEMIS_API_URL={{ lookup('env', 'ARTEMIS_ARTEMIS_API_URL') }}

### Config API ###
CONFIG_API_URL={{ lookup('env', 'ARTEMIS_CONFIG_API_URL') }}

### Elastic Search ###
ELASTIC_SEARCH_API_URL={{ lookup('env', 'ARTEMIS_ELASTIC_SEARCH_API_URL') }}
ELASTIC_SEARCH_API_KEY={{ lookup('env', 'ARTEMIS_ELASTIC_SEARCH_API_KEY') }}

### Storage path for config API ###
CONFIG_API_PATH_BRAND={{ lookup('env', 'ARTEMIS_CONFIG_API_PATH_BRAND') }}

### Onelogin SAML settings for Azure AD (Active Directory)
# Common settings for Visymo
VISYMO_AZURE_TENANT_ID={{ lookup('env', 'ARTEMIS_VISYMO_AZURE_TENANT_ID') }}
ONELOGIN_SAML_IDP_ENTITY_ID={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_IDP_ENTITY_ID') }}
ONELOGIN_SAML_IDP_SINGLE_SIGN_ON_SERVICE_URL={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_IDP_SINGLE_SIGN_ON_SERVICE_URL') }}
ONELOGIN_SAML_IDP_SINGLE_LOGOUT_SERVICE_URL={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_IDP_SINGLE_LOGOUT_SERVICE_URL') }}

# Application specific settings
ONELOGIN_SAML_BASE_URL={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_BASE_URL') }}
ONELOGIN_SAML_IDP_X509_CERT={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_IDP_X509_CERT') }}
ONELOGIN_SAML_SP_ASSERTION_CONSUMER_SERVICE_URL={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_SP_ASSERTION_CONSUMER_SERVICE_URL') }}
ONELOGIN_SAML_SP_ENTITY_ID={{ lookup('env', 'ARTEMIS_ONELOGIN_SAML_SP_ENTITY_ID') }}

### RabbitMQ ###
MQ_HOST={{ lookup('env', 'ARTEMIS_MQ_HOST') }}
MQ_PORT={{ lookup('env', 'ARTEMIS_MQ_PORT') }}
MQ_API_PORT={{ lookup('env', 'ARTEMIS_MQ_API_PORT') }}
MQ_USER={{ lookup('env', 'ARTEMIS_MQ_USER') }}
MQ_PASSWORD={{ lookup('env', 'ARTEMIS_MQ_PASSWORD') }}
MQ_VHOST={{ lookup('env', 'ARTEMIS_MQ_VHOST') }}

# LambdaTest Automation account
LAMBDA_TEST_USERNAME={{ lookup('env', 'ARTEMIS_LAMBDA_TEST_USERNAME') }}
LAMBDA_TEST_ACCESS_KEY={{ lookup('env', 'ARTEMIS_LAMBDA_TEST_ACCESS_KEY') }}

### Dev VM ###
DEV_VM_NAME={{ lookup('env', 'ARTEMIS_DEV_VM_NAME') }}

### JSON Template Schema ###
JSON_TEMPLATE_SCHEMA_PATH={{ lookup('env', 'ARTEMIS_JSON_TEMPLATE_SCHEMA_PATH') }}

### GITLAB API ###
GITLAB_API_URL={{ lookup('env', 'ARTEMIS_GITLAB_API_URL') }}
GITLAB_API_PROJECT_TOKEN='{{ lookup('env', 'ARTEMIS_GITLAB_API_PROJECT_TOKEN') }}'

### JSON Template Overrides ###
JSON_TEMPLATE_OVERRIDES_PATH={{ lookup('env', 'ARTEMIS_JSON_TEMPLATE_OVERRIDES_PATH') }}

### APIFY ###
APIFY_TOKEN={{ lookup('env', 'ARTEMIS_APIFY_TOKEN') }}
APIFY_API_URL={{ lookup('env', 'ARTEMIS_APIFY_API_URL') }}

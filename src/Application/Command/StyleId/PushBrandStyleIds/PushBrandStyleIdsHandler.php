<?php

declare(strict_types=1);

namespace Application\Command\StyleId\PushBrandStyleIds;

use Application\Command\StyleId\PushBrandStyleIds\Event\PushBrandStyleIdsWasFiredEvent;
use Application\Query\Brand\GetBrandStyleIds\GetBrandStyleIdsQuery;
use Application\Query\Brand\GetBrandStyleIds\GetBrandStyleIdsResponse;
use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\VbbStyleId\VbbStyleIdRepositoryInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;
use Visymo\QueryBus\QueryBus\QueryBusInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Domain\Event\EventDispatcherInterface;

final readonly class PushBrandStyleIdsHandler implements CommandHandlerInterface
{
    public function __construct(
        private QueryBusInterface $queryBus,
        private EventDispatcherInterface $eventDispatcher,
        private VbbStyleIdRepositoryInterface $vbbStyleIdRepository,
        private DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function handle(PushBrandStyleIdsCommand $command): void
    {
        /** @var GetBrandStyleIdsResponse $response */
        $response = $this->queryBus->handle(
            new GetBrandStyleIdsQuery($command->slug)
        );

        foreach ($response->styleIds as $styleId) {
            $vbbStyle = $this->vbbStyleIdRepository->findOneByStyleId(AdSenseStyleId::fromStyleId($styleId));

            if ($vbbStyle !== null) {
                $vbbStyle->lastPushedAt = $this->dateTimeFactory->createNow(TimezoneEnum::UTC);
                $this->vbbStyleIdRepository->store($vbbStyle);
            }
        }

        $this->eventDispatcher->dispatch(
            new PushBrandStyleIdsWasFiredEvent(
                slug    : $command->slug,
                styleIds: array_map('intval', $response->styleIds)
            ),
            PushBrandStyleIdsWasFiredEvent::NAME
        );
    }
}

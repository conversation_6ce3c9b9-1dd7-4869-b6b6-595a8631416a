<?php

declare(strict_types=1);

namespace Application\Command\JsonTemplate\UpdateJsonTemplateSchema;

use Domain\JsonTemplateSchema\JsonTemplateSchemaClientInterface;
use Domain\JsonTemplateSchema\JsonTemplateSchemaUpdaterInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;

readonly class UpdateJsonTemplateSchemaHandler implements CommandHandlerInterface
{
    public function __construct(
        private JsonTemplateSchemaClientInterface $jsonTemplateSchemaClient,
        private JsonTemplateSchemaUpdaterInterface $jsonTemplateSchemaUpdater
    )
    {
    }

    public function handle(UpdateJsonTemplateSchemaCommand $command): void
    {
        $content = $this->jsonTemplateSchemaClient->pullJsonTemplateSchema();
        $this->jsonTemplateSchemaUpdater->update($content);
    }
}

<?php

declare(strict_types=1);

namespace Application\Command\Scraper\ScheduleScraperBatch;

use Domain\Scraper\Exception\InvalidStatusCodeException;
use Domain\Scraper\ScraperBatch\ScraperBatchRepositoryInterface;
use Domain\Scraper\ScraperBatch\ScraperBatchStatus;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlRepositoryInterface;
use Domain\Scraper\ScraperClientRepositoryInterface;
use Psr\Log\LoggerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;

final readonly class ScheduleScraperBatchHandler implements CommandHandlerInterface
{
    public function __construct(
        private ScraperClientRepositoryInterface $scraperClientRepository,
        private ScraperBatchRepositoryInterface $scraperBatchRepository,
        private ScraperBatchUrlRepositoryInterface $scraperBatchUrlRepository,
        private LoggerInterface $logger
    )
    {
    }

    public function handle(ScheduleScraperBatchCommand $command): void
    {
        $scraperBatch = $this->scraperBatchRepository->findOneById($command->id);

        try {
            $scraperBatchUrls = $this->scraperBatchUrlRepository->findByScraperBatchIdAndRelatedShown($scraperBatch->id, false);
            $urls = [];

            foreach ($scraperBatchUrls as $scraperBatchUrl) {
                $urls[] = $scraperBatchUrl->url;
            }

            $runId = $this->scraperClientRepository->getClientForScraper(
                $scraperBatch->scraper
            )
                ->scheduleRun($urls);

            $scraperBatch->runId = $runId;
            $scraperBatch->increaseExecutionCount();
            $scraperBatch->status = ScraperBatchStatus::RUNNING;
        } catch (InvalidStatusCodeException $exception) {
            $this->logger->warning(
                'Failed to schedule scraper batch',
                [
                    'run_id'    => $scraperBatch->runId,
                    'exception' => $exception->getMessage(),
                ]
            );

            // To prevent continuous failing, we increase the execution count only if the status was already failing
            if ($scraperBatch->status === ScraperBatchStatus::FAILING) {
                $scraperBatch->increaseExecutionCount();
            }

            $scraperBatch->status = ScraperBatchStatus::FAILING;
        }

        $this->scraperBatchRepository->store($scraperBatch);
    }
}

<?php

declare(strict_types=1);

namespace Application\Command\Scraper\UpdateScraperBatchUrl;

use Domain\Scraper\ScraperBatch\Event\ScraperBatchWasIncompleteEvent;
use Domain\Scraper\ScraperBatch\ScraperBatch;
use Domain\Scraper\ScraperBatch\ScraperBatchRepositoryInterface;
use Domain\Scraper\ScraperBatch\ScraperBatchStatus;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrl;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlLogRepositoryInterface;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlRepositoryInterface;
use Psr\Log\LoggerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\Event\EventDispatcherInterface;

final readonly class UpdateScraperBatchUrlHandler implements CommandHandlerInterface
{
    public function __construct(
        private ScraperBatchUrlLogRepositoryInterface $scraperBatchUrlLogRepository,
        private ScraperBatchRepositoryInterface $scraperBatchRepository,
        private ScraperBatchUrlRepositoryInterface $scraperBatchUrlRepository,
        private DateTimeFactory $dateTimeFactory,
        private LoggerInterface $logger,
        private EventDispatcherInterface $eventDispatcher
    )
    {
    }

    public function handle(UpdateScraperBatchUrlCommand $command): void
    {
        $scraperBatch = $this->scraperBatchRepository->findOneById($command->id);
        $scraperBatchUrlResult = $this->scraperBatchUrlRepository->findByScraperBatchIdAndRelatedShown($scraperBatch->id, false);

        $scraperBatchUrls = [];

        foreach ($scraperBatchUrlResult as $scraperBatchUrl) {
            $scraperBatchUrls[$scraperBatchUrl->keyword] = $scraperBatchUrl;
        }

        try {
            $scraperBatchUrlLogs = $this->scraperBatchUrlLogRepository->findLastLogByKeywords(
                $scraperBatch->brand->slug,
                $scraperBatch->locale,
                $scraperBatch->searchRoute->path(),
                array_keys($scraperBatchUrls)
            );
        } catch (\Throwable $exception) {
            $this->logger->error(
                'Scraper batch url logs could not be retrieved',
                [
                    'run_id'  => $scraperBatch->runId,
                    'message' => $exception->getMessage(),
                ]
            );

            $scraperBatch->status = ScraperBatchStatus::FAILING;
            $this->scraperBatchRepository->store($scraperBatch);

            return;
        }

        if ($scraperBatchUrlLogs === []) {
            $this->logger->info('Scraper batch completed, batch has no logs', ['run_id' => $scraperBatch->runId]);
            $this->handleNewScheduledBatch($scraperBatch, $scraperBatchUrls);

            return;
        }

        $scraperBatchUrlsWithoutRelated = [];

        foreach ($scraperBatchUrlLogs as $scraperBatchUrlLog) {
            $scraperBatchUrl = $scraperBatchUrls[$scraperBatchUrlLog->keyword] ?? null;

            if ($scraperBatchUrl === null) {
                continue;
            }

            unset($scraperBatchUrls[$scraperBatchUrlLog->keyword]);

            $scraperBatchUrl->isRelatedShown = $scraperBatchUrlLog->isRelatedShown;

            if (!$scraperBatchUrl->isRelatedShown) {
                $scraperBatchUrlsWithoutRelated[] = $scraperBatchUrl;
            }
        }

        // Either there are urls without related or there are urls left without logs
        if ($scraperBatchUrlsWithoutRelated !== [] || $scraperBatchUrls !== []) {
            $this->logger->info(
                'Scraper batch completed, scheduling new batch for urls without related',
                [
                    'run_id' => $scraperBatch->runId,
                    'urls'   => count($scraperBatchUrlsWithoutRelated),
                ]
            );

            $this->handleNewScheduledBatch($scraperBatch, $scraperBatchUrls);

            return;
        }

        $this->logger->info('Scraper batch completed, batch has no urls left', ['run_id' => $scraperBatch->runId]);

        $scraperBatch->status = ScraperBatchStatus::COMPLETED;
        $scraperBatch->finishedAt = $this->dateTimeFactory->createNow();
        $this->scraperBatchRepository->store($scraperBatch);
        $this->scraperBatchUrlRepository->storeAll($scraperBatchUrls);
    }

    /**
     * @param ScraperBatchUrl[] $scraperBatchUrls
     */
    private function handleNewScheduledBatch(ScraperBatch $scraperBatch, array $scraperBatchUrls): void
    {
        $scraperBatch->status = ScraperBatchStatus::RUNNING;
        $this->scraperBatchRepository->store($scraperBatch);
        $this->scraperBatchUrlRepository->storeAll($scraperBatchUrls);

        $this->eventDispatcher->dispatch(
            new ScraperBatchWasIncompleteEvent($scraperBatch->id),
            ScraperBatchWasIncompleteEvent::NAME
        );
    }
}

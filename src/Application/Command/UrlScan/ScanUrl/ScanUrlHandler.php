<?php

declare(strict_types=1);

namespace Application\Command\UrlScan\ScanUrl;

use Domain\UrlScan\Exception\UrlScanNotFoundException;
use Domain\UrlScan\Scanner\UrlContentScannerInterface;
use Domain\UrlScan\UrlScanRepositoryInterface;
use Domain\UrlScanContent\UrlScanContentFactory;
use Domain\UrlScanContent\UrlScanContentRepositoryInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class ScanUrlHandler implements CommandHandlerInterface
{
    public function __construct(
        private UrlScanRepositoryInterface $urlScanRepository,
        private UrlScanContentRepositoryInterface $urlScanContentRepository,
        private UrlContentScannerInterface $urlContentScanner,
        private UrlScanContentFactory $urlScanContentFactory,
        private DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function handle(ScanUrlCommand $command): void
    {
        try {
            $urlScan = $this->urlScanRepository->findOneById($command->urlScanId);
        } catch (UrlScanNotFoundException) {
            // Ignore, possibly deleted by another process
            return;
        }

        $dateNow = \DateTimeImmutable::createFromMutable(
            $this->dateTimeFactory->createNow(TimezoneEnum::UTC)
        );
        $urlScan->lastCheckedAt = $dateNow;

        $actualUrlScanContent = $this->urlScanContentFactory->create(
            content     : $this->urlContentScanner->getContent($urlScan->url),
            downloadedAt: $dateNow,
            urlScan     : $urlScan
        );

        $currentUrlScanContent = $this->urlScanContentRepository->findOneByUrlScanIdAndChecksum(
            urlScanId: $urlScan->id,
            checksum : $actualUrlScanContent->checksum
        );

        if ($currentUrlScanContent !== null) {
            $currentUrlScanContent->lastFoundAt = $dateNow;
            $this->urlScanContentRepository->store($currentUrlScanContent);
        } else {
            $urlScan->lastNewContentFoundAt = $dateNow;
            $this->urlScanContentRepository->store($actualUrlScanContent);
        }

        $this->urlScanRepository->store($urlScan);
    }
}

<?php

declare(strict_types=1);

namespace Application\Command\Brand\RemoveBrand;

use Domain\AuditLog\AuditLogRepositoryInterface;
use Domain\Brand\BrandRepositoryInterface;
use Domain\Brand\Exception\BrandNotFoundException;
use Psr\Log\LoggerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;

final readonly class RemoveBrandH<PERSON>ler implements CommandHandlerInterface
{
    public function __construct(
        private BrandRepositoryInterface $brandRepository,
        private AuditLogRepositoryInterface $auditLogRepository,
        private LoggerInterface $logger
    )
    {
    }

    public function handle(RemoveBrandCommand $command): void
    {
        $brand = $this->brandRepository->findOneBySlug($command->slug);

        if ($brand === null) {
            throw BrandNotFoundException::createForSlug($command->slug);
        }

        $this->auditLogRepository->removeByObjectIdAndType(
            $brand->getAuditLogEntityId(),
            'brand'
        );
        $this->auditLogRepository->removeByObjectIdAndType(
            $brand->getAuditLogEntityId(),
            sprintf('antelope_brand.%s', $brand->slug)
        );

        $this->brandRepository->delete($brand);

        $this->logger->info(sprintf('Brand %s has been deleted', $command->slug));
    }
}

<?php

declare(strict_types=1);

namespace Application\Command\Brand\UpdateBrandConfigChecksum;

use Application\Command\Brand\UpdateBrandConfigArtemisChecksum\UpdateBrandConfigArtemisChecksumCommand;
use Application\Command\Brand\UpdateBrandConfigBrandWebsiteChecksum\UpdateBrandConfigBrandWebsiteChecksumCommand;
use Domain\AntelopeApi\Client\AntelopeApiClientInterface;
use Domain\AntelopeBrand\AntelopeBrandRepositoryInterface;
use Domain\AntelopeBrand\Exception\AntelopeBrandNotFoundException;
use Domain\AntelopeBrand\File\AntelopeBrandFileRepositoryInterface;
use Domain\AntelopeBrand\File\Exception\AntelopeBrandFileNotFoundException;
use Domain\BrandConfigChecksum\BrandConfigChecksumFactory;
use Domain\BrandConfigChecksum\BrandConfigChecksumRepositoryInterface;
use Domain\Generic\ChecksumGenerator;
use Psr\Log\LoggerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandSeries;

final readonly class UpdateBrandConfigChecksumHandler implements CommandHandlerInterface
{
    public function __construct(
        private AntelopeApiClientInterface $antelopeApiClient,
        private AntelopeBrandRepositoryInterface $antelopeBrandRepository,
        private AntelopeBrandFileRepositoryInterface $antelopeBrandFileRepository,
        private BrandConfigChecksumRepositoryInterface $brandConfigChecksumRepository,
        private BrandConfigChecksumFactory $brandConfigChecksumFactory,
        private ChecksumGenerator $checksumGenerator,
        private CommandBusInterface $commandBus,
        private LoggerInterface $logger
    ) {
    }

    public function handle(UpdateBrandConfigChecksumCommand $command): void
    {
        // Antelope
        $antelopeBrand = $this->antelopeBrandRepository->findOneBySlug($command->slug);

        if ($antelopeBrand === null) {
            throw AntelopeBrandNotFoundException::createForSlug($command->slug);
        }

        $antelopeBrandFile = $this->antelopeBrandFileRepository->findOneBySlug(
            slug   : $command->slug
        );

        if ($antelopeBrandFile === null) {
            throw AntelopeBrandFileNotFoundException::create($command->slug);
        }

        $antelopeProductionChecksum = $this->fetchAntelopeProductionChecksum(
            $command->slug
        );
        $antelopeFileChecksum = $antelopeBrandFile->getChecksum();
        $antelopeStoredChecksum = $this->checksumGenerator->generate($antelopeBrand->config);

        $brandConfigChecksum = $this->brandConfigChecksumRepository->findOneBySlug($command->slug);

        if ($brandConfigChecksum !== null) {
            $brandConfigChecksum->antelopeProductionChecksum = $antelopeProductionChecksum;
            $brandConfigChecksum->antelopeFileChecksum = $antelopeFileChecksum;
            $brandConfigChecksum->antelopeStoredChecksum = $antelopeStoredChecksum;
        } else {
            $brandConfigChecksum = $this->brandConfigChecksumFactory->create(
                brand                     : $antelopeBrand->brand,
                antelopeProductionChecksum: $antelopeProductionChecksum,
                antelopeFileChecksum      : $antelopeFileChecksum,
                antelopeStoredChecksum    : $antelopeStoredChecksum,
                artemisChecksum           : null,
                brandWebsiteChecksum      : null
            );
        }

        $this->brandConfigChecksumRepository->store($brandConfigChecksum);

        // Artemis and brand website
        $this->commandBus->handle(
            new CommandSeries(
                new UpdateBrandConfigArtemisChecksumCommand($command->slug),
                new UpdateBrandConfigBrandWebsiteChecksumCommand($command->slug)
            )
        );
    }

    private function fetchAntelopeProductionChecksum(string $slug): ?string
    {
        try {
            $config = $this->antelopeApiClient->getBrandConfig($slug);

            return $this->checksumGenerator->generate($config);
        } catch (\Throwable $exception) {
            $this->logger->notice(
                'Failed to fetch Antelope production checksum for {slug}: {message}',
                [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                    'slug'      => $slug,
                ]
            );

            return null;
        }
    }
}

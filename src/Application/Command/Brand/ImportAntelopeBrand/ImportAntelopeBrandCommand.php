<?php

declare(strict_types=1);

namespace Application\Command\Brand\ImportAntelopeBrand;

use Visymo\CommandBus\Domain\CommandBus\CommandInterface;

final readonly class ImportAntelopeBrandCommand implements CommandInterface
{
    /**
     * @param array<string, mixed>|null $config
     */
    public function __construct(
        public string $slug,
        public ?array $config,
        public bool $force
    )
    {
    }
}

<?php

declare(strict_types=1);

namespace Application\Command\Brand\PushBrandAssets;

use Domain\AntelopeBrand\Exception\AntelopeBrandIsEmptyException;
use Domain\AntelopeBrand\Exception\AntelopeBrandNotFoundException;
use Domain\Brand\BrandValidatorInterface;
use Domain\BrandAssets\Config\BrandAssetsConfigFactory;
use Domain\BrandAssets\Event\BrandAssetsWasPushedEvent;
use Domain\BrandAssets\Exception\BrandAssetsNotFoundException;
use Domain\BrandConfig\Exception\BrandConfigException;
use Domain\ConfigApi\Client\ConfigApiClientInterface;
use Psr\Log\LoggerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Domain\Event\EventDispatcherInterface;

final readonly class PushBrandAssetsHandler implements CommandHandlerInterface
{
    public function __construct(
        private BrandAssetsConfigFactory $brandAssetsConfigFactory,
        private BrandValidatorInterface $brandValidator,
        private ConfigApiClientInterface $configApiClient,
        private DateTimeFactory $dateTimeFactory,
        private EventDispatcherInterface $eventDispatcher,
        private LoggerInterface $logger
    )
    {
    }

    public function handle(PushBrandAssetsCommand $command): void
    {
        try {
            $brandAssetsConfig = $this->brandAssetsConfigFactory->create($command->slug);

            $this->brandValidator->validateBrandPush($command->slug);

            $this->configApiClient->updateBrandAssets(
                slug  : $brandAssetsConfig->brandAssets->brand->slug,
                config: $brandAssetsConfig->toArray()
            );

            $this->eventDispatcher->dispatch(
                new BrandAssetsWasPushedEvent(
                    slug    : $brandAssetsConfig->brandAssets->brand->slug,
                    pushedAt: $this->dateTimeFactory->createNow(TimezoneEnum::UTC)
                ),
                BrandAssetsWasPushedEvent::NAME
            );
        } catch (BrandConfigException $exception) {
            $this->logger->error(
                'Cannot push brand assets for {brand}: {message}',
                [
                    'brand'     => $command->slug,
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                ]
            );
        } catch (AntelopeBrandNotFoundException|AntelopeBrandIsEmptyException $exception) {
            $this->logger->error(
                'Brand {brand} cannot be pushed, reason: Missing Antelope config',
                [
                    'brand'     => $command->slug,
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                ]
            );
        } catch (BrandAssetsNotFoundException $exception) {
            $this->logger->error(
                'Brand {brand} cannot be pushed, reason: Missing assets',
                [
                    'brand'     => $command->slug,
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                ]
            );
        }
    }
}

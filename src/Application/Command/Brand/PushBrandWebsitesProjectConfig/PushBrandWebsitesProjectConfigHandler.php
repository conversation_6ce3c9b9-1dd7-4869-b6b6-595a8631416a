<?php

declare(strict_types=1);

namespace Application\Command\Brand\PushBrandWebsitesProjectConfig;

use Application\Query\Brand\GetProjectConfig\GetProjectConfigQuery;
use Application\Query\Brand\GetProjectConfig\GetProjectConfigResponse;
use Domain\ConfigApi\Client\ConfigApiClientInterface;
use Domain\PushConfigChecksum\PushConfigChecksumHelper;
use Visymo\CommandBus\Domain\CommandBus\CommandHandlerInterface;
use Visymo\QueryBus\QueryBus\QueryBusInterface;

final readonly class PushBrandWebsitesProjectConfigHandler implements CommandHandlerInterface
{
    private const string CONFIG_CHECKSUM_NAME = 'brand-websites-project';

    public function __construct(
        private QueryBusInterface $queryBus,
        private PushConfigChecksumHelper $pushConfigChecksumHelper,
        private ConfigApiClientInterface $configApiClient
    )
    {
    }

    public function handle(PushBrandWebsitesProjectConfigCommand $command): void
    {
        /** @var GetProjectConfigResponse $response */
        $response = $this->queryBus->handle(
            new GetProjectConfigQuery()
        );

        if (!$this->shouldPushConfig($command->force, $response)) {
            return;
        }

        $this->configApiClient->updateProjectConfig(
            project: 'brand-websites',
            config : $response->toArray()
        );

        $this->pushConfigChecksumHelper->storeChecksum(
            name    : self::CONFIG_CHECKSUM_NAME,
            checksum: $response->getChecksum()
        );
    }

    private function shouldPushConfig(
        bool $force,
        GetProjectConfigResponse $response
    ): bool
    {
        if ($force) {
            return true;
        }

        return !$this->pushConfigChecksumHelper->equalsChecksum(
            name    : self::CONFIG_CHECKSUM_NAME,
            checksum: $response->getChecksum()
        );
    }
}

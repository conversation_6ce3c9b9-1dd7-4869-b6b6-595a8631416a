<?php

declare(strict_types=1);

namespace Application\Query\Brand\GetBrands\Model;

use Domain\BrandAssets\Image\BrandAssetsImageFileName;

final readonly class BrandResult
{
    /**
     * @param DomainResult[] $domainResults
     */
    public function __construct(
        public string $name,
        public string $slug,
        public bool $active,
        public ?string $partnerSlug,
        public string $primaryColor,
        public GoogleAdSenseResult $googleAdSenseResult,
        public string $logoSvg,
        public array $domainResults
    )
    {
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'name'           => $this->name,
            'slug'           => $this->slug,
            'active'         => $this->active,
            'partner_slug'   => $this->partnerSlug,
            'primary_color'  => $this->primaryColor,
            'google_adsense' => $this->googleAdSenseResult->toArray(),
            'images'         => [
                BrandAssetsImageFileName::LOGO_SVG->value => $this->logoSvg,
            ],
            'domains'        => array_map(
                static fn (DomainResult $domainResult) => $domainResult->toArray(),
                $this->domainResults
            ),
        ];
    }
}

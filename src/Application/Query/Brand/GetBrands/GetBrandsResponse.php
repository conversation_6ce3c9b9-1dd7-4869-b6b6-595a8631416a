<?php

declare(strict_types=1);

namespace Application\Query\Brand\GetBrands;

use Application\Query\Brand\GetBrands\Model\BrandResult;
use Domain\Generic\ChecksumGenerator;
use Visymo\QueryBus\QueryBus\QueryResponseInterface;

final class GetBrandsResponse implements QueryResponseInterface
{
    private string $checksum;

    /**
     * @param BrandResult[] $results
     */
    public function __construct(
        private readonly ChecksumGenerator $checksumGenerator,
        public readonly array $results
    )
    {
    }

    public function getChecksum(): string
    {
        if (!isset($this->checksum)) {
            $this->checksum = $this->checksumGenerator->generate($this->getConfig());
        }

        return $this->checksum;
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return [
            'checksum' => $this->getChecksum(),
            ...$this->getConfig(),
        ];
    }

    /**
     * @return mixed[]
     */
    private function getConfig(): array
    {
        return [
            'brands' => array_map(
                static fn (BrandResult $result) => $result->toArray(),
                $this->results
            ),
        ];
    }
}

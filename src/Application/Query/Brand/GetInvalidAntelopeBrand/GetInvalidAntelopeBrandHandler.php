<?php

declare(strict_types=1);

namespace Application\Query\Brand\GetInvalidAntelopeBrand;

use Domain\AntelopeBrand\AntelopeBrandRepositoryInterface;
use Domain\AntelopeBrand\Validator\AntelopeBrandValidator;
use Visymo\QueryBus\QueryBus\QueryHandlerInterface;
use Visymo\QueryBus\QueryBus\QueryResponseInterface;

final readonly class GetInvalidAntelopeBrandHandler implements QueryHandlerInterface
{
    public function __construct(
        private AntelopeBrandRepositoryInterface $antelopeBrandRepository,
        private AntelopeBrandValidator $antelopeBrandValidator
    )
    {
    }

    public function handle(GetInvalidAntelopeBrandQuery $query): QueryResponseInterface
    {
        $antelopeBrand = $this->antelopeBrandRepository->findOneBySlug($query->slug);

        if ($antelopeBrand === null || $antelopeBrand->invalidConfig === []) {
            return new GetInvalidAntelopeBrandResponse(null);
        }

        $validationException = null;

        try {
            $this->antelopeBrandValidator->validate($antelopeBrand->invalidConfig);
        } catch (\Throwable $exception) {
            $validationException = $exception;
        }

        return new GetInvalidAntelopeBrandResponse(
            [
                'exception'      => $validationException?->getMessage(),
                'invalid_config' => $antelopeBrand->invalidConfig,
            ]
        );
    }
}

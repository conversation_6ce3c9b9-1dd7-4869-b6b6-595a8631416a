<?php

declare(strict_types=1);

namespace Application\Query\Brand\GetBrandStyleIds;

use Domain\Brand\Brand;
use Visymo\QueryBus\QueryBus\QueryResponseInterface;

final readonly class GetBrandStyleIdsResponse implements QueryResponseInterface
{
    /**
     * @param string[] $styleIds
     */
    public function __construct(
        public Brand $brand,
        public array $styleIds
    )
    {
    }
}

<?php

declare(strict_types=1);

namespace Application\Query\Brand\GetBrandAdSenseStyleIdTable;

use Application\Query\Brand\GetBrandAdSenseStyleIdTable\Model\BrandAdSenseStyleIdRow;
use Application\Query\Brand\GetBrandAdSenseStyleIdTable\Model\BrandAdSenseStyleIdRowFactory;
use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\Brand\BrandRepositoryInterface;
use Domain\BrandModule\AdSenseStyleId\BrandModuleAdSenseStyleIdInterface;
use Domain\BrandModule\Property\BrandModulePropertyReader;
use Symfony\Component\String\UnicodeString;
use Visymo\QueryBus\QueryBus\QueryHandlerInterface;

final readonly class GetBrandAdSenseStyleIdTableHandler implements QueryHandlerInterface
{
    public function __construct(
        private BrandRepositoryInterface $brandRepository,
        private BrandAdSenseStyleIdRowFactory $brandAdSenseStyleIdRowFactory,
        private BrandModulePropertyReader $brandModulePropertyReader
    ) {
    }

    public function handle(GetBrandAdSenseStyleIdTableQuery $query): GetBrandAdSenseStyleIdTableResponse
    {
        $tableHeaders = $this->getTableHeaders();
        $tableRows = $this->getTableRows($tableHeaders);
        $tableHeaders = $this->formatTableHeaders($tableHeaders);

        return new GetBrandAdSenseStyleIdTableResponse(
            tableHeaders: $tableHeaders,
            tableRows: $tableRows
        );
    }

    /**
     * @return array<string, string[]>
     */
    private function getTableHeaders(): array
    {
        $moduleStyleIdPropertyMap = [];

        foreach ($this->brandModulePropertyReader->get() as $property) {
            if (!$property->supportsModule(BrandModuleAdSenseStyleIdInterface::class)) {
                continue;
            }

            $reflectionClass = new \ReflectionClass($property->brandModuleClass);

            foreach ($reflectionClass->getProperties() as $moduleProperty) {
                $propertyName = $moduleProperty->getName();
                /** @var \ReflectionNamedType|null $adSenseStyleIdModulePropertyType */
                $adSenseStyleIdModulePropertyType = $moduleProperty->getType();

                if ($adSenseStyleIdModulePropertyType?->getName() === AdSenseStyleId::class) {
                    $moduleStyleIdPropertyMap[$property->name][] = str_replace(
                        ['styleId', 'StyleId'],
                        ['', ' '],
                        $propertyName
                    );
                }
            }
        }

        return $moduleStyleIdPropertyMap;
    }

    /**
     * @param array<string, string[]> $tableHeaders
     *
     * @return BrandAdSenseStyleIdRow[]
     */
    private function getTableRows(array $tableHeaders): array
    {
        $rows = [];
        $modulePropertyCount = array_map(
            static fn ($moduleProperties) => count($moduleProperties),
            $tableHeaders
        );

        foreach ($this->brandRepository->findAll() as $brand) {
            $rows[] = $this->brandAdSenseStyleIdRowFactory->create(
                $brand,
                $modulePropertyCount
            );
        }

        return $rows;
    }

    /**
     * @param array<string, string[]> $tableHeaders
     *
     * @return array<string, string[]>
     */
    private function formatTableHeaders(array $tableHeaders): array
    {
        $formattedHeaders = [];

        foreach ($tableHeaders as $moduleName => $moduleProperties) {
            $formattedModuleName = (new UnicodeString($moduleName))
                ->replaceMatches('/([a-z])([A-Z])/', '$1 $2')
                ->toString();

            $formattedHeaders[$formattedModuleName] = $moduleProperties;
        }

        return $formattedHeaders;
    }
}

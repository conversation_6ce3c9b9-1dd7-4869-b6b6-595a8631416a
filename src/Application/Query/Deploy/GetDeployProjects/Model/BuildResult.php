<?php

declare(strict_types=1);

namespace Application\Query\Deploy\GetDeployProjects\Model;

class BuildResult
{
    /**
     * @param string[] $availableOnServers
     * @param string[] $notAvailableOnServers
     */
    public function __construct(
        public string $build,
        public string $buildVersion,
        public bool $active,
        public bool $valid,
        public bool $equalsLatestMasterCommit,
        public array $availableOnServers,
        public array $notAvailableOnServers
    )
    {
    }

    public function isAvailableOnAllServers(): bool
    {
        if ($this->availableOnServers === []) {
            return false;
        }

        return $this->notAvailableOnServers === [];
    }

    public function canBeActivated(): bool
    {
        if ($this->active) {
            return false;
        }

        if (!$this->valid) {
            return false;
        }

        return $this->isAvailableOnAllServers();
    }

    public function canBeMadeInvalid(): bool
    {
        if ($this->active) {
            return false;
        }

        return $this->valid;
    }
}

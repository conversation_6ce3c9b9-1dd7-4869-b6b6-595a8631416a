<?php

declare(strict_types=1);

namespace Application\Query\GetScraperUrls;

use Application\Query\GetScraperUrls\Result\ScraperUrlResult;
use Visymo\QueryBus\QueryBus\QueryHandlerInterface;

final readonly class GetScraperUrlsHandler implements QueryHandlerInterface
{
    public function handle(GetScraperUrlsQuery $query): GetScraperUrlsResponse
    {
        $results = [];

        foreach ($query->keywords as $keyword) {
            $queryString = http_build_query(
                [
                    'q'      => $keyword,
                    'locale' => $query->locale,
                    'tv'     => $query->templateVariant,
                ]
            );
            $url = sprintf(
                'https://%s%s?%s',
                $query->host,
                $query->searchRoute->path(),
                $queryString
            );

            $results[] = new ScraperUrlResult(
                url    : $url,
                keyword: $keyword
            );
        }

        return new GetScraperUrlsResponse($results);
    }
}

<?php

declare(strict_types=1);

namespace Application\Query\BrandConfigChecksum\GetBrandConfigChecksums;

use Visymo\QueryBus\QueryBus\QueryResponseInterface;

final readonly class GetBrandConfigChecksumsResponse implements QueryResponseInterface
{
    /**
     * @param array<string, string|null> $checksums
     */
    public function __construct(
        public array $checksums
    )
    {
    }

    public function getChecksumForBrand(string $slug): ?string
    {
        return $this->checksums[$slug] ?? null;
    }
}

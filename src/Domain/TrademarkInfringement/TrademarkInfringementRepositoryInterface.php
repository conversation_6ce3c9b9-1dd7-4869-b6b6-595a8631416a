<?php

declare(strict_types=1);

namespace Domain\TrademarkInfringement;

use Domain\TrademarkInfringement\Exception\TrademarkInfringementAlreadyExistsException;
use Domain\TrademarkInfringement\Exception\TrademarkInfringementNotFoundException;

interface TrademarkInfringementRepositoryInterface
{
    /**
     * @throws TrademarkInfringementNotFoundException
     */
    public function findOneById(int $id): TrademarkInfringement;

    public function findOneByQuery(string $query): ?TrademarkInfringement;

    /**
     * @return TrademarkInfringement[]
     */
    public function findAll(): array;

    /**
     * @throws TrademarkInfringementAlreadyExistsException
     */
    public function store(TrademarkInfringement $trademarkInfringement): void;

    public function delete(TrademarkInfringement $trademarkInfringement): void;
}

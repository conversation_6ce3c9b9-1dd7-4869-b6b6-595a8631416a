<?php

declare(strict_types=1);

namespace Domain\TrademarkInfringement;

use Domain\AuditLog\AuditLoggableInterface;

class TrademarkInfringement implements AuditLoggableInterface
{
    public int $id;

    /**
     * @param string[]|null $locales
     */
    public function __construct(
        public string $query,
        public TrademarkInfringementMatchType $matchType,
        public ?array $locales,
        public \DateTimeInterface $createdAt
    )
    {
        if ($this->locales === []) {
            $this->locales = null;
        }
    }

    public function hasId(): bool
    {
        return isset($this->id);
    }

    public function getAuditLogEntityId(): int
    {
        return $this->id;
    }

    public function getAuditLogEntityName(): string
    {
        return 'trademark_infringement';
    }

    public function getAuditLogEntityLabel(): string
    {
        return $this->query;
    }

    /**
     * @inheritDoc
     */
    public function modifyAuditLog(array $changes): array
    {
        return $changes;
    }
}

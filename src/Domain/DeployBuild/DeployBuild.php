<?php

declare(strict_types=1);

namespace Domain\DeployBuild;

use Domain\DeployBuild\Exception\InvalidBuildVersionException;
use Domain\DeployProject\DeployProject;

class DeployBuild
{
    public int $id;

    public function __construct(
        public DeployProject $project,
        public string $build,
        public ?string $commitSha,
        public bool $invalid
    )
    {
    }

    public function getBuildVersion(): string
    {
        if (preg_match('~(v\d+)$~', $this->build, $matches) === 1) {
            return $matches[1];
        }

        throw new InvalidBuildVersionException($this->build, $this->project->name);
    }
}

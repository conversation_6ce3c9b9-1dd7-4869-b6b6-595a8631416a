<?php

declare(strict_types=1);

namespace Domain\Generic;

use Visymo\Shared\Domain\DateTime\TimezoneEnum;

class UtcDate extends \DateTime
{
    public function __construct(string $date)
    {
        parent::__construct($date, TimezoneEnum::UTC->toDateTimeZone());
    }

    public static function createFromDateTime(\DateTimeInterface $dateTime): self
    {
        return new self(
            $dateTime->format('Y-m-d')
        );
    }

    public function __toString(): string
    {
        return $this->format('Y-m-d');
    }
}

<?php

declare(strict_types=1);

namespace Domain\JsonTemplate\Exception;

use Domain\Scraper\Exception\ScraperException;

class JsonTemplateFailingValidationException extends ScraperException
{
    /**
     * @param string[] $messages
     */
    public function __construct(
        string $message,
        int $code,
        ?\Throwable $previous,
        public readonly array $messages
    )
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * @param string[] $messages
     */
    public static function create(array $messages): self
    {
        return new self(
            'Invalid JSON template preview data',
            0,
            null,
            $messages
        );
    }
}

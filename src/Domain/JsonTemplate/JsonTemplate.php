<?php

declare(strict_types=1);

namespace Domain\JsonTemplate;

use Domain\Brand\Brand;
use Domain\BrandModule\AbstractBrandModule;

class JsonTemplate extends AbstractBrandModule
{
    public Brand $brand;

    public function __construct(
        public ?string $templateVariant,
        public ?string $templateOverrides
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'json_template';
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): void
    {
        $this->brand = $brand;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        $templateOverrides = $this->templateOverrides ?? '';
        $templateOverrides = $templateOverrides === '' ? null : explode(',', $templateOverrides);

        return [
            'template_variant'   => $this->templateVariant,
            'template_overrides' => $templateOverrides,
        ];
    }
}

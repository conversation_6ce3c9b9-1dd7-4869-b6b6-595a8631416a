<?php

declare(strict_types=1);

namespace Domain\DeployStats;

final readonly class DeployServerStats
{
    /**
     * @param mixed[] $data
     */
    public function __construct(
        private array $data
    )
    {
    }

    public function getServer(): string
    {
        return $this->data['server'];
    }

    /**
     * @return string[]
     */
    public function getDeploys(): array
    {
        return $this->data['deploys'];
    }

    public function getPreviousSymlink(): ?string
    {
        return $this->data['symlinks']['previous'];
    }

    public function getCurrentSymlink(): string
    {
        return $this->data['symlinks']['current'];
    }

    public function getStartedAt(): \DateTime
    {
        return new \DateTime(
            sprintf('@%s', $this->data['timestamps']['start']),
            new \DateTimeZone('UTC')
        );
    }

    public function getEndedAt(): \DateTime
    {
        return new \DateTime(
            sprintf('@%s', $this->data['timestamps']['end']),
            new \DateTimeZone('UTC')
        );
    }
}

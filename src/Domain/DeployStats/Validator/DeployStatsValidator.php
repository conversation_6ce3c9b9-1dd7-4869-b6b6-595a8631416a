<?php

declare(strict_types=1);

namespace Domain\DeployStats\Validator;

use Domain\DeployStats\Exception\DeployStatsValidationFailedException;
use Visymo\Shared\Domain\Validator\ValidatorInterface;

final readonly class DeployStatsValidator
{
    public function __construct(
        private ValidatorInterface $deployStatsJsonSchemaValidator
    )
    {
    }

    /**
     * @param mixed[] $data
     *
     * @throws DeployStatsValidationFailedException
     */
    public function validate(array $data): void
    {
        try {
            $this->deployStatsJsonSchemaValidator->assert($data);
        } catch (\Throwable $exception) {
            throw DeployStatsValidationFailedException::create($exception);
        }
    }
}

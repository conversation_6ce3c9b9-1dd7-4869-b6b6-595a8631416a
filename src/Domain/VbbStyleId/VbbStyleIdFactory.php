<?php

declare(strict_types=1);

namespace Domain\VbbStyleId;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\Brand\Brand;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class VbbStyleIdFactory implements VbbStyleIdFactoryInterface
{
    public function __construct(
        private DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function create(AdSenseStyleId $styleId, Brand $brand): VbbStyleId
    {
        return new VbbStyleId(
            styleId       : $styleId,
            brand         : $brand,
            lastImportedAt: $this->dateTimeFactory->createNow(TimezoneEnum::UTC),
            lastPushedAt  : null
        );
    }
}

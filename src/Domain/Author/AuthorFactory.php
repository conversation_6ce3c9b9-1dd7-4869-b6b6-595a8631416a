<?php

declare(strict_types=1);

namespace Domain\Author;

final readonly class AuthorFactory
{
    public function create(
        string $slug,
        string $name,
        ?string $image = null
    ): Author
    {
        return new Author(
            slug : $slug,
            name : $name,
            image: $image
        );
    }

    public function createNew(): Author
    {
        return new Author(
            slug : '',
            name : '',
            image: null
        );
    }
}

<?php

declare(strict_types=1);

namespace Domain\Author;

class Author
{
    public const string EDITORIAL_TEAM = 'editorial_team';

    public int $id;

    public function __construct(
        public string $slug,
        public string $name,
        public ?string $image = null
    )
    {
    }

    /**
     * @return array<string,mixed>
     */
    public function toArray(): array
    {
        return [
            'slug'  => $this->slug,
            'name'  => $this->name,
            'image' => $this->image,
        ];
    }
}

<?php

declare(strict_types=1);

namespace Domain\DeployProject;

interface DeployProjectRepositoryInterface
{
    public function findOneByGitLabProjectId(int $gitLabProjectId): ?DeployProject;

    public function findOneByName(string $name): ?DeployProject;

    /**
     * @return DeployProject[]
     */
    public function findAll(): array;

    public function store(DeployProject $deployProject): void;
}

<?php

declare(strict_types=1);

namespace Domain\DeployProject\Exception;

class NotAllowedToDeployProjectException extends DeployProjectException
{
    public static function create(string $project, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Not allowed to deploy project "%s" at the moment', $project),
            0,
            $previous
        );
    }
}

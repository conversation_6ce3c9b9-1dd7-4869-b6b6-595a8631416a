<?php

declare(strict_types=1);

namespace Domain\DeployProject\Exception;

class DeployProjectNotFoundException extends DeployProjectException
{
    public static function createWithGitLabProjectId(int $gitLabProjectId, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('No deploy project found with GitLab project ID %u', $gitLabProjectId),
            0,
            $previous
        );
    }

    public static function createWithName(string $name, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('No deploy project found with name "%s"', $name),
            0,
            $previous
        );
    }
}

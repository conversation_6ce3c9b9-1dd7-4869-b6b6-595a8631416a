<?php

declare(strict_types=1);

namespace Domain\AuditLog;

use Domain\User\User;

class AuditLogFactory
{
    /**
     * @param mixed[] $data
     */
    public function create(
        int $objectId,
        string $objectType,
        string $objectLabel,
        AuditLogAction $action,
        array $data,
        \DateTime $changedAt,
        ?User $user
    ): AuditLog
    {
        $auditLog = new AuditLog(
            objectId   : $objectId,
            objectType : $objectType,
            objectLabel: $objectLabel,
            action     : $action,
            data       : $data,
            changedAt  : $changedAt,
        );
        $auditLog->user = $user;

        return $auditLog;
    }
}

<?php

declare(strict_types=1);

namespace Domain\AdSenseStyleId;

use Domain\BrandAdSenseStyleId\BrandAdSenseStyleIdRegistry;
use Domain\VbbStyleId\VbbStyleIdRepositoryInterface;

final readonly class AdSenseStyleIdRegistryChecker
{
    public function __construct(
        private BrandAdSenseStyleIdRegistry $brandAdSenseStyleIdRegistry,
        private VbbStyleIdRepositoryInterface $vbbStyleIdRepository
    )
    {
    }

    public function checkStatus(AdSenseStyleId $styleId, string $slug): AdSenseStyleIdStatus
    {
        if ($this->isVbbStyleId($styleId)) {
            return AdSenseStyleIdStatus::VBB;
        }

        return $this->isUnique($styleId, $slug) ? AdSenseStyleIdStatus::VALID : AdSenseStyleIdStatus::NOT_UNIQUE;
    }

    private function isUnique(AdSenseStyleId $adSenseStyleId, string $slug): bool
    {
        $brands = $this->brandAdSenseStyleIdRegistry->getCollection()->getBrandsWithAdSenseStyleId((string)$adSenseStyleId);
        $brands = array_flip($brands);
        unset($brands[$slug]);

        return $brands === [];
    }

    private function isVbbStyleId(AdSenseStyleId $adSenseStyleId): bool
    {
        return $this->vbbStyleIdRepository->findOneByStyleId($adSenseStyleId) !== null;
    }
}

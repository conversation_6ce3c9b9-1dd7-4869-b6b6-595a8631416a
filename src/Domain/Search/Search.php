<?php

declare(strict_types=1);

namespace Domain\Search;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\Brand\Brand;
use Domain\BrandModule\AbstractBrandModule;
use Domain\BrandModule\AdSenseStyleId\BrandModuleAdSenseStyleIdInterface;
use Domain\BrandModule\Enabled\BrandModuleEnabledInterface;
use Domain\BrandModule\HomeRoute\BrandModuleHomeRouteInterface;
use Domain\BrandModule\SearchRoute\BrandModuleSearchRouteInterface;
use Domain\SearchRoute\SearchRoute;

class Search extends AbstractBrandModule implements
    BrandModuleAdSenseStyleIdInterface,
    BrandModuleEnabledInterface,
    BrandModuleHomeRouteInterface,
    BrandModuleSearchRouteInterface
{
    public Brand $brand;

    public function __construct(
        public bool $enabled,
        public ?bool $seoEnabled,
        public ?AdSenseStyleId $styleIdDesktop,
        public ?AdSenseStyleId $styleIdMobile
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'search';
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): void
    {
        $this->brand = $brand;
    }

    /**
     * @inheritDoc
     */
    public function getAdSenseStyleIds(): array
    {
        $adSenseStyleIds = [
            $this->styleIdDesktop,
            $this->styleIdMobile,
        ];

        return array_filter(
            $adSenseStyleIds,
            static fn (?AdSenseStyleId $styleId): bool => $styleId !== null
        );
    }

    /**
     * @inheritDoc
     */
    public static function getAdSenseStyleIdDependentSearchRoutes(): array
    {
        return [
            ...self::getSearchRoutes(),
            SearchRoute::KEYWORD_IDEAS_SEARCH,
            SearchRoute::CONTENT_SEARCH,
            SearchRoute::CONTENT_SEARCH_ADVERTISED,
        ];
    }

    /**
     * @inheritDoc
     */
    public static function getSearchRoutes(): array
    {
        return [
            SearchRoute::SEARCH,
        ];
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        $data = [
            self::KEY_ENABLED => $this->enabled,
        ];

        if ($this->enabled) {
            $data['seo_enabled'] = $this->seoEnabled;
            $data['style_id_desktop'] = $this->styleIdDesktop;
            $data['style_id_mobile'] = $this->styleIdMobile;
        }

        return $data;
    }
}

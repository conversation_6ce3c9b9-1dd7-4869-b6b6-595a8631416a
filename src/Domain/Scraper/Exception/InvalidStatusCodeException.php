<?php

declare(strict_types=1);

namespace Domain\Scraper\Exception;

class InvalidStatusCodeException extends ScraperException
{
    public static function create(int $statusCode, string $scraper, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Invalid status code "%d" for %s, probably because actor is busy', $statusCode, $scraper),
            0,
            $previous
        );
    }
}

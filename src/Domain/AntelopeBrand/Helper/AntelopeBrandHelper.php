<?php

declare(strict_types=1);

namespace Domain\AntelopeBrand\Helper;

use Domain\AntelopeBrand\AntelopeBrandRepositoryInterface;

final readonly class AntelopeBrandHelper
{
    public function __construct(
        private AntelopeBrandRepositoryInterface $antelopeBrandRepository
    )
    {
    }

    public function getHostForBrandAndLocale(string $slug, string $locale): ?string
    {
        $antelopeBrand = $this->antelopeBrandRepository->findOneBySlug($slug);

        foreach ($antelopeBrand?->config['domains'] ?? [] as $host => $domainData) {
            foreach ($domainData['locales'] as $localeData) {
                if ($localeData['locale'] === $locale) {
                    return $host;
                }
            }
        }

        return null;
    }
}

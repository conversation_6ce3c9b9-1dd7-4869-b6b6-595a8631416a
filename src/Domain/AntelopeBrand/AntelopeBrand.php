<?php

declare(strict_types=1);

namespace Domain\AntelopeBrand;

use Domain\Brand\Brand;
use Domain\Brand\ContractType\AdSenseContractType;

class AntelopeBrand
{
    public Brand $brand;

    /** @var mixed[] */
    public array $config = [];

    /** @var mixed[] */
    public array $invalidConfig = [];

    /**
     * @param mixed[] $config
     */
    public function __construct(array $config, AntelopeBrandStatus $antelopeBrandStatus)
    {
        $this->setConfig($config, $antelopeBrandStatus);
    }

    /**
     * @param mixed[] $config
     */
    public function setConfig(array $config, AntelopeBrandStatus $antelopeBrandStatus): self
    {
        if ($antelopeBrandStatus->isSuccess()) {
            $this->config = $config;
            $this->invalidConfig = [];
        } else {
            $this->invalidConfig = $config;
        }

        return $this;
    }

    public function getSlug(): string
    {
        return $this->config['brand']['slug'];
    }

    public function getName(): string
    {
        return $this->config['brand']['name'];
    }

    public function getStatus(): AntelopeBrandStatus
    {
        if ($this->invalidConfig !== []) {
            return AntelopeBrandStatus::VALIDATION_ERROR;
        }

        return AntelopeBrandStatus::SUCCESS;
    }

    public function hasBingAdsApproval(): bool
    {
        return (bool)($this->config['brand']['bing_ads']['approval'] ?? false);
    }

    public function hasGoogleAdSenseApproval(): bool
    {
        return (bool)($this->config['brand']['google_adsense']['approval'] ?? false);
    }

    public function getPartnerSlug(): ?string
    {
        return $this->config['brand']['partner_slug'] ?? null;
    }

    public function getAdSenseContractType(): ?AdSenseContractType
    {
        if (isset($this->config['brand']['google_adsense']['contract_type'])) {
            return AdSenseContractType::tryFrom($this->config['brand']['google_adsense']['contract_type']);
        }

        return null;
    }

    public function getFirstDomain(): ?string
    {
        $firstDomain = array_key_first($this->config['domains'] ?? []);

        return $firstDomain !== null ? (string)$firstDomain : null;
    }
}

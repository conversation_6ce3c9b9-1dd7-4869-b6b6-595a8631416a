<?php

declare(strict_types=1);

namespace Domain\BrandAssets;

use Domain\AuditLog\AuditLoggableInterface;
use Domain\Brand\Brand;
use Domain\BrandAssets\Exception\BrandAssetsImageNotFoundException;
use Domain\BrandAssets\Image\BrandAssetsImageFileName;

class BrandAssets implements AuditLoggableInterface
{
    public Brand $brand;

    public const string VARIABLE_BRAND_PRIMARY_COLOR = 'brand-primary-color';

    /**
     * @param mixed[] $config
     */
    public function __construct(
        public array $config
    )
    {
    }

    public function getBrandPrimaryColor(): string
    {
        return $this->config['variables'][self::VARIABLE_BRAND_PRIMARY_COLOR];
    }

    public function getImageContent(
        BrandAssetsImageFileName $imageFileName,
        bool $binary = false
    ): string
    {
        $content = $this->config['images'][$imageFileName->value] ?? null;

        if ($content === null) {
            throw BrandAssetsImageNotFoundException::create($imageFileName);
        }

        return $binary ? $this->convertBase64ToBinary($content) : $content;
    }

    /**
     * @throws \JsonException
     */
    public function getConfigAsJson(): string
    {
        return json_encode(
            $this->config,
            JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES
        );
    }

    /**
     * @throws \JsonException
     */
    public function setConfigFromJson(string $json): void
    {
        $this->config = json_decode($json, true, 512, JSON_THROW_ON_ERROR);
    }

    public function getAuditLogEntityId(): int
    {
        return $this->brand->id;
    }

    public function getAuditLogEntityName(): string
    {
        return 'brand.assets';
    }

    public function getAuditLogEntityLabel(): string
    {
        return $this->brand->slug;
    }

    /**
     * @inheritDoc
     */
    public function modifyAuditLog(array $changes): array
    {
        // Do not log the content of the changes, we only need to know that it was changed
        foreach (array_keys($changes) as $attribute) {
            $changes[$attribute] = ['old', 'new'];
        }

        return $changes;
    }

    private function convertBase64ToBinary(string $content): string
    {
        // Content is the full base64 content, we need to extract the base64 content from the data URI
        // Like 'data:image/png;base64,iVBORw0KGgoAAAANSUhEU...'
        $content = explode(',', $content)[1];

        return (string)base64_decode($content, true);
    }
}

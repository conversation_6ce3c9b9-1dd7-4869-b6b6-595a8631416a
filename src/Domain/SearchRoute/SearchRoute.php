<?php

declare(strict_types=1);

namespace Domain\SearchRoute;

enum SearchRoute: string
{
    case ARTICLE                      = 'route_article';
    case CONTENT_SEARCH               = 'route_content_search';
    case CONTENT_SEARCH_ADVERTISED    = 'route_content_search_advertised';
    case DISPLAY_SEARCH               = 'route_display_search';
    case DISPLAY_SEARCH_ADVERTISED    = 'route_display_search_advertised';
    case DISPLAY_SEARCH_RELATED       = 'route_display_search_related';
    case DISPLAY_SEARCH_RELATED_WEB   = 'route_display_search_related_web';
    case KEYWORD_IDEAS_SEARCH         = 'route_keyword_ideas_search';
    case MICROSOFT_SEARCH             = 'route_microsoft_search';
    case MICROSOFT_SEARCH_RELATED     = 'route_microsoft_search_related';
    case MICROSOFT_SEARCH_RELATED_WEB = 'route_microsoft_search_related_web';
    case SEARCH                       = 'route_search';
    case WEB_SEARCH                   = 'route_web_search';
    case WEB_SEARCH_ADVERTISED        = 'route_web_search_advertised';

    public function labelWithPath(): string
    {
        return sprintf('%s (%s)', $this->label(), $this->path());
    }

    private function label(): string
    {
        return match ($this) {
            self::ARTICLE                      => 'Article',
            self::CONTENT_SEARCH               => 'Content search',
            self::CONTENT_SEARCH_ADVERTISED    => 'Content search advertised',
            self::DISPLAY_SEARCH               => 'Display search',
            self::DISPLAY_SEARCH_ADVERTISED    => 'Display search advertised',
            self::DISPLAY_SEARCH_RELATED       => 'Display search related',
            self::DISPLAY_SEARCH_RELATED_WEB   => 'Display search related web',
            self::KEYWORD_IDEAS_SEARCH         => 'Keyword ideas search',
            self::MICROSOFT_SEARCH             => 'Microsoft search',
            self::MICROSOFT_SEARCH_RELATED     => 'Microsoft search related',
            self::MICROSOFT_SEARCH_RELATED_WEB => 'Microsoft search related web',
            self::SEARCH                       => 'Search',
            self::WEB_SEARCH                   => 'Web search',
            self::WEB_SEARCH_ADVERTISED        => 'Web search advertised',
        };
    }

    public function path(): string
    {
        return match ($this) {
            self::ARTICLE                      => '/article',
            self::CONTENT_SEARCH               => '/cs',
            self::CONTENT_SEARCH_ADVERTISED    => '/csa',
            self::DISPLAY_SEARCH               => '/ds',
            self::DISPLAY_SEARCH_ADVERTISED    => '/dsa',
            self::DISPLAY_SEARCH_RELATED       => '/dsr',
            self::DISPLAY_SEARCH_RELATED_WEB   => '/dsrw',
            self::KEYWORD_IDEAS_SEARCH         => '/kis',
            self::MICROSOFT_SEARCH             => '/ms',
            self::MICROSOFT_SEARCH_RELATED     => '/msr',
            self::MICROSOFT_SEARCH_RELATED_WEB => '/msrw',
            self::SEARCH                       => '/search',
            self::WEB_SEARCH                   => '/ws',
            self::WEB_SEARCH_ADVERTISED        => '/wsa',
        };
    }

    public function getWildcardPath(): string
    {
        return match ($this) {
            self::ARTICLE        => '/article/*',
            self::CONTENT_SEARCH => '/cs/*',
            default              => $this->path(),
        };
    }
}

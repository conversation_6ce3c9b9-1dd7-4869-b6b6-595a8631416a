<?php

declare(strict_types=1);

namespace Domain\DeployServer;

use Domain\DeployBuild\DeployBuild;
use Domain\DeployProject\DeployProject;

class DeployServer
{
    public int $id;

    /**
     * @param DeployBuild[] $availableBuilds
     */
    public function __construct(
        public DeployProject $project,
        public string $name,
        public iterable $availableBuilds,
        public ?DeployBuild $activeBuild,
        public ?\DateTime $startedAt,
        public ?\DateTime $endedAt,
        public \DateTime $updatedAt
    )
    {
    }
}

<?php

declare(strict_types=1);

namespace Domain\DeployServer;

interface DeployServerRepositoryInterface
{
    public function findOneByDeployProjectIdAndName(int $deployProjectId, string $name): ?DeployServer;

    /**
     * @return DeployServer[]
     */
    public function findByDeployProjectId(int $deployProjectId): array;

    public function delete(DeployServer $deployServer): void;

    public function store(DeployServer $deployServer): void;
}

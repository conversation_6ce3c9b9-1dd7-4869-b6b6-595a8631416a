<?php

declare(strict_types=1);

namespace Domain\Monetization;

use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class MonetizationFactory
{
    public function create(
        bool $adsEnabled,
        bool $relatedTermsEnabled,
        bool $displayBannersEnabled
    ): Monetization
    {
        return new Monetization(
            adsEnabled           : $adsEnabled,
            relatedTermsEnabled  : $relatedTermsEnabled,
            displayBannersEnabled: $displayBannersEnabled,
            updatedAt            : new \DateTimeImmutable('now', TimezoneEnum::UTC->toDateTimeZone()),
        );
    }
}

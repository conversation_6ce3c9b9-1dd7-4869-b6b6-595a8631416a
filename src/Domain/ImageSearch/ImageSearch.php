<?php

declare(strict_types=1);

namespace Domain\ImageSearch;

use Domain\Brand\Brand;
use Domain\BrandModule\AbstractBrandModule;
use Domain\BrandModule\Enabled\BrandModuleEnabledInterface;

class ImageSearch extends AbstractBrandModule implements BrandModuleEnabledInterface
{
    public Brand $brand;

    public function __construct(
        public bool $enabled
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'image_search';
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): void
    {
        $this->brand = $brand;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::KEY_ENABLED => $this->enabled,
        ];
    }
}

<?php

declare(strict_types=1);

namespace Domain\BrandConfigChecksum;

use Domain\Brand\Brand;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final readonly class BrandConfigChecksumFactory
{
    public function __construct(
        private DateTimeFactory $dateTimeFactory
    )
    {
    }

    public function create(
        Brand $brand,
        ?string $antelopeProductionChecksum,
        ?string $antelopeFileChecksum,
        ?string $antelopeStoredChecksum,
        ?string $artemisChecksum,
        ?string $brandWebsiteChecksum
    ): BrandConfigChecksum
    {
        $brandConfigChecksum = new BrandConfigChecksum(
            antelopeProductionChecksum: $antelopeProductionChecksum,
            antelopeFileChecksum      : $antelopeFileChecksum,
            antelopeStoredChecksum    : $antelopeStoredChecksum,
            artemisChecksum           : $artemisChecksum,
            brandWebsiteChecksum      : $brandWebsiteChecksum,
            updatedAt                 : $this->dateTimeFactory->createNow(TimezoneEnum::UTC)
        );
        $brandConfigChecksum->brand = $brand;

        return $brandConfigChecksum;
    }
}

<?php

declare(strict_types=1);

namespace Domain\BrandConfigChecksum;

use Domain\BrandConfigChecksum\Exception\BrandConfigChecksumAlreadyExistsException;

interface BrandConfigChecksumRepositoryInterface
{
    public function findOneBySlug(string $slug): ?BrandConfigChecksum;

    /**
     * @return \Generator<BrandConfigChecksum>
     */
    public function findAll(): \Generator;

    /**
     * @throws BrandConfigChecksumAlreadyExistsException
     */
    public function store(BrandConfigChecksum $brandConfigChecksum): void;
}

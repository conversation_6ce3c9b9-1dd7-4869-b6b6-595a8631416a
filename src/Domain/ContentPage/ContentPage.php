<?php

declare(strict_types=1);

namespace Domain\ContentPage;

use Domain\Author\Author;
use Domain\Brand\Brand;
use Domain\BrandModule\AbstractBrandModule;
use Domain\BrandModule\Enabled\BrandModuleEnabledInterface;
use Domain\BrandModule\SearchRoute\BrandModuleSearchRouteDependencyInterface;
use Domain\SearchRoute\SearchRoute;

class ContentPage extends AbstractBrandModule implements
    BrandModuleEnabledInterface,
    BrandModuleSearchRouteDependencyInterface
{
    public Brand $brand;

    public function __construct(
        public bool $enabled,
        public ?string $collection,
        public ?Author $author,
        public bool $useBrandForOrganicResults,
        public ?SearchRoute $organicResultRoute
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'content_page';
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): void
    {
        $this->brand = $brand;
    }

    /**
     * @inheritDoc
     */
    public function getSearchRouteDependencies(): array
    {
        if (!$this->enabled) {
            return [];
        }

        if (!$this->useBrandForOrganicResults) {
            return [];
        }

        if ($this->organicResultRoute === null) {
            return [];
        }

        return [$this->organicResultRoute];
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        $data = [
            self::KEY_ENABLED => $this->enabled,
        ];

        if ($this->enabled) {
            $authorData = $this->author?->toArray();

            if ($authorData !== null) {
                unset($authorData['image']);
            }

            $data['collection'] = $this->collection;
            $data['author'] = $authorData;
            $data['use_brand_for_organic_results'] = $this->useBrandForOrganicResults;
            $data['organic_result_route'] = $this->organicResultRoute?->value;
        }

        return $data;
    }
}

<?php

declare(strict_types=1);

namespace Domain\User;

use Domain\User\Security\SecurityRole;

class User
{
    public const string KEY_THEME = 'theme';

    public int $id;

    /** @var SecurityRole[] */
    public array $roles = [];

    public function __construct(
        public string $email,
        public string $firstName,
        public string $lastName,
        public UserTheme $theme
    )
    {
    }

    public function getFullName(): string
    {
        return sprintf('%s %s', $this->firstName, $this->lastName);
    }

    public function hasRole(SecurityRole $role): bool
    {
        return in_array($role, $this->roles, true);
    }
}

<?php

declare(strict_types=1);

namespace Domain\GitLabApi\Commit;

final class CommitFactory
{
    /**
     * @param mixed[] $data
     */
    public function createFromArray(array $data): Commit
    {
        $message = null;

        if (isset($data['message'])) {
            $message = trim((string)$data['message']);
        }

        return new Commit(
            id        : (string)$data['id'],
            shortId   : (string)$data['short_id'],
            title     : (string)$data['title'],
            message   : $message,
            authorName: (string)$data['author_name']
        );
    }
}

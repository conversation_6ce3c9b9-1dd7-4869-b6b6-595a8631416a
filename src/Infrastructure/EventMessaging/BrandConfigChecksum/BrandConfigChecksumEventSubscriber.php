<?php

declare(strict_types=1);

namespace Infrastructure\EventMessaging\BrandConfigChecksum;

use Application\Query\Brand\GetBrandConfigInSyncResults\GetBrandConfigInSyncResultsQuery;
use Application\Query\Brand\GetBrandConfigInSyncResults\GetBrandConfigInSyncResultsResponse;
use Infrastructure\UserInterface\Web\FlashMessage\FlashMessageRepository;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Session\Session;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Visymo\QueryBus\QueryBus\QueryBusInterface;

final readonly class BrandConfigChecksumEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private QueryBusInterface $queryBus,
        private FlashMessageRepository $flashMessageRepository
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::RESPONSE => 'onKernelResponse',
        ];
    }

    public function onKernelResponse(ResponseEvent $event): void
    {
        $request = $event->getRequest();

        if (!$event->isMainRequest() || $request->isXmlHttpRequest() || !$event->getResponse()->isSuccessful()) {
            return;
        }

        /** @var Session $session */
        $session = $request->getSession();

        if (!$session->isStarted()) {
            return;
        }

        /** @var GetBrandConfigInSyncResultsResponse $response */
        $response = $this->queryBus->handle(
            new GetBrandConfigInSyncResultsQuery()
        );

        $brandSlugs = [];

        foreach ($response->getOutOfSyncResults() as $brandConfigResult) {
            $brandSlugs[] = $brandConfigResult->slug;
        }

        if ($brandSlugs === []) {
            return;
        }

        $amountOfBrands = count($brandSlugs);
        $message = implode(', ', $brandSlugs);

        if ($amountOfBrands > 3) {
            $message = sprintf('%s and %d more.', implode(', ', array_slice($brandSlugs, 0, 3)), $amountOfBrands - 3);
        }

        $this->flashMessageRepository->addWarning(
            sprintf('The config of the following brands is out of sync: %s', $message)
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\EventMessaging\ScraperBatch;

use Application\Command\Scraper\ScheduleScraperBatch\ScheduleScraperBatchCommand;
use Application\Command\Scraper\UpdateScraperBatchUrl\UpdateScraperBatchUrlCommand;
use Domain\Scraper\ScraperBatch\Event\ScraperBatchWasCompletedEvent;
use Domain\Scraper\ScraperBatch\Event\ScraperBatchWasIncompleteEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;

readonly class ScraperBatchEventSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private CommandBusInterface $commandBus
    )
    {
    }

    /**
     * @inheritDoc
     */
    public static function getSubscribedEvents(): array
    {
        return [
            ScraperBatchWasCompletedEvent::NAME  => 'onScraperBatchWasCompleted',
            ScraperBatchWasIncompleteEvent::NAME => 'onScraperBatchWasIncomplete',
        ];
    }

    public function onScraperBatchWasCompleted(ScraperBatchWasCompletedEvent $event): void
    {
        $this->commandBus->handle(
            new UpdateScraperBatchUrlCommand($event->id)
        );
    }

    public function onScraperBatchWasIncomplete(ScraperBatchWasIncompleteEvent $event): void
    {
        $this->commandBus->handle(
            new ScheduleScraperBatchCommand($event->id)
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand\Validator;

use Domain\ContentPage\ContentPage;
use Domain\ContentPageHome\ContentPageHome;
use Symfony\Component\Form\FormInterface;

class ContentPageHomeFormValidator implements BrandFormValidatorInterface
{
    /**
     * @inheritDoc
     */
    public function validate(FormInterface $form): array
    {
        $contentPageHomeForm = $form->get('contentPageHome');
        $validationErrors = [];

        /** @var ContentPageHome|null $contentPageHome */
        $contentPageHome = $contentPageHomeForm->getData();

        if ($contentPageHome?->isEnabled() !== true) {
            return $validationErrors;
        }

        $contentPageForm = $form->get('contentPage');

        /** @var ContentPage|null $contentPage */
        $contentPage = $contentPageForm->getData();

        if ($contentPage?->isEnabled() !== true) {
            $validationErrors[] = new BrandValidationError(
                module : 'contentPageHome',
                field  : 'enabled',
                message: 'The content page module must be enabled if the content page home module is enabled.'
            );
        }

        return $validationErrors;
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\ContentPageHome\ContentPageHome;
use Domain\ContentPageHome\ContentPageHomeType;
use Domain\SearchRoute\SearchRoute;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleContentPageHomeType extends AbstractBrandModuleType
{
    private const string FIELD_TYPE         = 'type';
    private const string FIELD_SEARCH_ROUTE = 'searchRoute';

    public static function getBrandModuleClass(): string
    {
        return ContentPageHome::class;
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                self::FIELD_TYPE,
                EnumType::class,
                [
                    'class'        => ContentPageHomeType::class,
                    'multiple'     => false,
                    'expanded'     => false,
                    'required'     => false,
                    'choice_label' => static fn (ContentPageHomeType $type) => $type->value,
                ]
            )
            ->add(
                self::FIELD_SEARCH_ROUTE,
                EnumType::class,
                [
                    'class'        => SearchRoute::class,
                    'multiple'     => false,
                    'expanded'     => false,
                    'required'     => false,
                    'choices'      => [
                        SearchRoute::WEB_SEARCH,
                        SearchRoute::WEB_SEARCH_ADVERTISED,
                        SearchRoute::DISPLAY_SEARCH_RELATED,
                        SearchRoute::DISPLAY_SEARCH_RELATED_WEB,
                    ],
                    'choice_label' => static fn (SearchRoute $route) => $route->labelWithPath(),
                ]
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => function (FormInterface $form): ?ContentPageHome {
                    if (!$this->isEnabled($form)) {
                        return null;
                    }

                    $type = $form->get(self::FIELD_TYPE)->getData();
                    $searchRoute = $form->get(self::FIELD_SEARCH_ROUTE)->getData();

                    return new ContentPageHome(
                        enabled    : true,
                        type       : $type,
                        searchRoute: $searchRoute
                    );
                },
                'setter'     => static function (Brand $brand, ?ContentPageHome $contentPageHome): void {
                    $brand->setContentPageHome($contentPageHome);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        if (!$this->isEnabled($form)) {
            return;
        }

        $typeField = $form->get(self::FIELD_TYPE);
        $searchRouteField = $form->get(self::FIELD_SEARCH_ROUTE);

        if ($typeField->getData() === null) {
            $typeField->addError(
                new FormError('Type is required when the module is enabled.')
            );
        }

        if ($searchRouteField->getData() === null) {
            $searchRouteField->addError(
                new FormError('A search route is required when the module is enabled.')
            );
        }
    }
}

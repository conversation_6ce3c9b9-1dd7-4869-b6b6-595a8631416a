<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\DisplaySearch\DisplaySearch;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleDisplaySearchType extends AbstractBrandModuleType
{
    public static function getBrandModuleClass(): string
    {
        return DisplaySearch::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => fn (FormInterface $form): DisplaySearch => new DisplaySearch($this->isEnabled($form)),
                'setter'     => static function (Brand $brand, ?DisplaySearch $displaySearch): void {
                    $brand->setDisplaySearch($displaySearch);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        // Display Search only has an enabled field.
    }
}

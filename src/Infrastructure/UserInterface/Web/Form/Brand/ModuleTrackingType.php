<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\Tracking\Tracking;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ModuleTrackingType extends AbstractBrandModuleType
{
    private const string FIELD_CAMPAIGN_NAME_VALIDATION_ENABLED = 'campaignNameValidationEnabled';

    public static function getBrandModuleClass(): string
    {
        return Tracking::class;
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder->add(self::FIELD_CAMPAIGN_NAME_VALIDATION_ENABLED);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => static function (FormInterface $form): ?Tracking {
                    $campaignNameValidationEnabled = (bool)$form
                        ->get(self::FIELD_CAMPAIGN_NAME_VALIDATION_ENABLED)
                        ->getData();

                    if (!$campaignNameValidationEnabled) {
                        return null;
                    }

                    return new Tracking(true);
                },
                'setter'     => static function (Brand $brand, ?Tracking $tracking): void {
                    $brand->setTracking($tracking);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        // No validation needed
    }
}

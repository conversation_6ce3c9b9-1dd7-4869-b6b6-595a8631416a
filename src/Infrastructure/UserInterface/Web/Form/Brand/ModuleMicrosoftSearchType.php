<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\MicrosoftSearch\MicrosoftSearch;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleMicrosoftSearchType extends AbstractBrandModuleType
{
    public static function getBrandModuleClass(): string
    {
        return MicrosoftSearch::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => function (FormInterface $form): ?MicrosoftSearch {
                    if (!$this->isEnabled($form)) {
                        return null;
                    }

                    return new MicrosoftSearch(
                        enabled: true
                    );
                },
                'setter'     => static function (Brand $brand, ?MicrosoftSearch $microsoftSearch): void {
                    $brand->setMicrosoftSearch($microsoftSearch);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        // Not needed
    }
}

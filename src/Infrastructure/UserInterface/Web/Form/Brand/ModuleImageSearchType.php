<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\ImageSearch\ImageSearch;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleImageSearchType extends AbstractBrandModuleType
{
    public static function getBrandModuleClass(): string
    {
        return ImageSearch::class;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => fn (FormInterface $form): ImageSearch => new ImageSearch($this->isEnabled($form)),
                'setter'     => static function (Brand $brand, ?ImageSearch $imageSearch): void {
                    $brand->setImageSearch($imageSearch);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        // Image Search only has an enabled field.
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\MicrosoftSearchRelated\MicrosoftSearchRelated;
use Infrastructure\UserInterface\Web\Form\Generic\AdSenseStyleIdType;
use <PERSON>ymfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleMicrosoftSearchRelatedType extends AbstractBrandModuleType
{
    private const string FIELD_STYLE_ID_DESKTOP = 'styleIdDesktop';
    private const string FIELD_STYLE_ID_MOBILE  = 'styleIdMobile';

    public static function getBrandModuleClass(): string
    {
        return MicrosoftSearchRelated::class;
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(self::FIELD_STYLE_ID_DESKTOP, AdSenseStyleIdType::class)
            ->add(self::FIELD_STYLE_ID_MOBILE, AdSenseStyleIdType::class);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => fn (FormInterface $form): MicrosoftSearchRelated => new MicrosoftSearchRelated(
                    enabled       : $this->isEnabled($form),
                    styleIdDesktop: $form->get(self::FIELD_STYLE_ID_DESKTOP)->getData(),
                    styleIdMobile : $form->get(self::FIELD_STYLE_ID_MOBILE)->getData(),
                ),
                'setter'     => static function (Brand $brand, ?MicrosoftSearchRelated $microsoftSearchRelated): void {
                    $brand->setMicrosoftSearchRelated($microsoftSearchRelated);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        // No additional validation needed.
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\BrandModule\Property\BrandModulePropertyReader;
use Domain\User\Security\SecurityRole;
use Domain\User\User;
use Infrastructure\UserInterface\Web\Form\Brand\Validator\BrandFormValidatorInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class BrandType extends AbstractType
{
    /**
     * @param BrandFormValidatorInterface[] $validators
     * @param AbstractBrandModuleType[]     $brandModuleForms
     */
    public function __construct(
        private readonly User $user,
        private readonly BrandModulePropertyReader $brandModulePropertyReader,
        private readonly iterable $validators,
        private readonly iterable $brandModuleForms

    )
    {
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->add(Brand::KEY_ACTIVE);

        $brandModuleToPropertyMapping = [];

        foreach ($this->brandModulePropertyReader->get() as $property) {
            $brandModuleToPropertyMapping[$property->brandModuleClass] = $property->name;
        }

        foreach ($this->brandModuleForms as $brandModuleForm) {
            $brandModuleClass = $brandModuleForm::getBrandModuleClass();
            $builder->add(
                child: $brandModuleToPropertyMapping[$brandModuleClass],
                type : $brandModuleForm::class
            );
        }

        $builder
            ->add('save', SubmitType::class)
            ->addEventListener(FormEvents::SUBMIT, fn (FormEvent $event) => $this->validateForm($event));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => Brand::class,
                'disabled'   => !$this->user->hasRole(SecurityRole::MAINTAINER),
            ]
        );
    }

    public function validateForm(FormEvent $event): void
    {
        $form = $event->getForm();

        foreach ($this->validators as $validator) {
            $validationErrors = $validator->validate($form);

            foreach ($validationErrors as $validationError) {
                $formError = new FormError($validationError->message);
                $moduleForm = $form->get($validationError->module);

                if ($validationError->field !== null) {
                    $moduleForm->get($validationError->field)->addError($formError);
                } else {
                    $moduleForm->addError($formError);
                }
            }
        }
    }
}

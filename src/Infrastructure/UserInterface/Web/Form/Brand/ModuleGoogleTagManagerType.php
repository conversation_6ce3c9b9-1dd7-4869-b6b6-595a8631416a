<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\GoogleTagManager\GoogleTagManager;
use Domain\SearchRoute\SearchRoute;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleGoogleTagManagerType extends AbstractBrandModuleType
{
    private const string FIELD_GOOGLE_TAG_MANAGER_ID = 'googleTagManagerId';
    private const string FIELD_ROUTES                = 'routes';

    public static function getBrandModuleClass(): string
    {
        return GoogleTagManager::class;
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(self::FIELD_GOOGLE_TAG_MANAGER_ID)
            ->add(
                self::FIELD_ROUTES,
                EnumType::class,
                [
                    'class'        => SearchRoute::class,
                    'multiple'     => true,
                    'expanded'     => true,
                    'required'     => false,
                    'choices'      => [
                        SearchRoute::WEB_SEARCH,
                        SearchRoute::WEB_SEARCH_ADVERTISED,
                        SearchRoute::DISPLAY_SEARCH_RELATED,
                        SearchRoute::DISPLAY_SEARCH_RELATED_WEB,
                    ],
                    'choice_label' => static fn (SearchRoute $route) => $route->labelWithPath(),
                ]
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => function (FormInterface $form): ?GoogleTagManager {
                    if (!$this->isEnabled($form)) {
                        return null;
                    }

                    $googleTagManagerId = $form->get(self::FIELD_GOOGLE_TAG_MANAGER_ID)->getData();
                    $routes = $form->get(self::FIELD_ROUTES)->getData();

                    return new GoogleTagManager(
                        enabled           : true,
                        googleTagManagerId: $googleTagManagerId,
                        routes            : $routes
                    );
                },
                'setter'     => static function (Brand $brand, ?GoogleTagManager $googleTagManager): void {
                    $brand->setGoogleTagManager($googleTagManager);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        if (!$this->isEnabled($form)) {
            return;
        }

        $googleTagManagerIdField = $form->get(self::FIELD_GOOGLE_TAG_MANAGER_ID);
        $routesField = $form->get(self::FIELD_ROUTES);

        if ($googleTagManagerIdField->getData() === null) {
            $googleTagManagerIdField->addError(
                new FormError('Google Tag Manager ID is required when the module is enabled.')
            );
        }

        if ($routesField->getData() === []) {
            $routesField->addError(
                new FormError('At least one route must be selected when the module is enabled.')
            );
        }
    }
}

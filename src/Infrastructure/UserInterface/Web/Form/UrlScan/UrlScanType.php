<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\UrlScan;

use Domain\UrlScan\Exception\InvalidUrlException;
use Domain\UrlScan\UrlScan;
use Domain\UrlScan\UrlScanNormalizer;
use Domain\UrlScan\UrlScanRepositoryInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\UrlType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Url;

final class UrlScanType extends AbstractType
{
    public function __construct(
        private readonly UrlScanRepositoryInterface $urlScanRepository,
        private readonly UrlScanNormalizer $urlScanNormalizer
    )
    {
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add(
                'url',
                UrlType::class,
                [
                    'label'            => 'URL',
                    'required'         => true,
                    'default_protocol' => null,
                    'attr'             => [
                        'maxlength' => 255,
                    ],
                    'constraints'      => [
                        new Length(['max' => 255]),
                        new NotBlank(),
                        new Url(message: 'Invalid URL given'),
                    ],
                ]
            )
            ->add('Add', SubmitType::class)
            ->addEventListener(FormEvents::POST_SUBMIT, fn (FormEvent $event) => $this->validateForm($event));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => UrlScan::class,
            ]
        );
    }

    private function validateForm(FormEvent $event): void
    {
        /** @var UrlScan $urlScan */
        $urlScan = $event->getData();
        $urlField = $event->getForm()->get('url');

        if (!$urlField->isValid()) {
            return;
        }

        try {
            $urlScan->url = $this->urlScanNormalizer->normalizeUrl($urlScan->url);
        } catch (InvalidUrlException) {
            $urlField->addError(
                new FormError('Invalid URL given')
            );

            return;
        }

        $urlExists = $this->urlScanRepository->findOneByUrl($urlScan->url) !== null;

        if ($urlExists) {
            $urlField->addError(
                new FormError('URL already exists')
            );
        }
    }
}

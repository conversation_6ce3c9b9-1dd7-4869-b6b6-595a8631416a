<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\JsonTemplate;

use Domain\Brand\BrandRepositoryInterface;
use Infrastructure\UserInterface\Web\Form\Generic\DatalistType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;
use Visymo\Shared\Domain\Locale\Locale;

final class JsonTemplatePreviewType extends AbstractType
{
    private const string FIELD_SLUG          = 'slug';
    private const string FIELD_LOCALE        = 'locale';
    private const string FIELD_JSON_TEMPLATE = 'json_template';

    public function __construct(
        private readonly BrandRepositoryInterface $brandRepository
    )
    {
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $brandOptions = [];

        foreach ($this->brandRepository->findAll() as $brand) {
            $brandOptions[$brand->slug] = $brand->slug;
        }

        $builder
            ->add(
                self::FIELD_SLUG,
                ChoiceType::class,
                [
                    'multiple'    => false,
                    'expanded'    => false,
                    'required'    => true,
                    'choices'     => $brandOptions,
                    'constraints' => [
                        new NotBlank(),
                    ],
                    'label_attr'  => ['class' => 'form__label form__label--inline'],
                ]
            )
            ->add(
                self::FIELD_LOCALE,
                DatalistType::class,
                [
                    'constraints' => [
                        new NotBlank(),
                    ],
                    'items'       => Locale::SUPPORTED_LOCALES,
                    'mapped'      => false,
                    'placeholder' => 'Enter or choose a locale',
                    'label_attr'  => ['class' => 'form__label form__label--inline'],
                ]
            )
            ->add(
                self::FIELD_JSON_TEMPLATE,
                HiddenType::class,
                [
                    'constraints' => [
                        new NotBlank(),
                    ],
                    'empty_data'  => '',
                ]
            )
            ->add(
                'save',
                SubmitType::class,
                [
                    'attr'  => ['class' => 'button'],
                    'label' => 'Preview',
                ]
            );
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => null,
            ]
        );
    }
}

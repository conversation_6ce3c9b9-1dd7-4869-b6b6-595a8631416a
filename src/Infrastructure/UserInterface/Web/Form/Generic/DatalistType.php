<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Generic;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\Form\FormView;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class DatalistType extends AbstractType
{
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'items'       => [],
                'placeholder' => '',
                'compound'    => false,
            ]
        );
    }

    /**
     * @param mixed[] $options
     */
    public function buildView(FormView $view, FormInterface $form, array $options): void
    {
        $view->vars['items'] = $options['items'];
        $view->vars['placeholder'] = $options['placeholder'];
    }

    public function getBlockPrefix(): string
    {
        return 'datalist';
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder->addEventListener(
            FormEvents::SUBMIT,
            static function (FormEvent $event) use ($options): void {
                $value = $event->getData();

                if (in_array($value, $options['items'], true)) {
                    return;
                }

                if ((bool)$options['required'] || (string)$value !== '') {
                    $event->getForm()->addError(
                        new FormError('Please select a valid value.')
                    );
                }
            }
        );
    }
}

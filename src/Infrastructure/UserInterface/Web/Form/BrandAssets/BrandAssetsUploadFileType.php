<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\BrandAssets;

use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\FileType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;

class BrandAssetsUploadFileType extends AbstractType
{
    public const string FIELD_ZIP_FILE = 'zipFile';

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add(
                self::FIELD_ZIP_FILE,
                FileType::class,
                [
                    'label'    => 'Brand assets (ZIP file)',
                    'required' => true,
                    'attr'     => [
                        'accept' => '.zip',
                    ],
                ]
            )
            ->add('validate', SubmitType::class);
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Validator;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Symfony\Component\Validator\Attribute\HasNamedArguments;
use Symfony\Component\Validator\Constraints\Regex;

final class AdSenseStyleIdConstraint extends Regex
{
    #[HasNamedArguments]
    public function __construct(
        ?array $groups = null,
        mixed $payload = null
    )
    {
        $message = 'The AdSense style ID "{{ value }}" contains illegal characters:';
        $message .= ' it must be a 10-digit number starting with 1 or higher.';

        parent::__construct(
            pattern: AdSenseStyleId::PATTERN,
            message: $message,
            groups : $groups,
            payload: $payload
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Validator;

use Symfony\Component\Validator\Constraint;

class MaxLinesConstraint extends Constraint
{
    public int $maxLines;
    public string $message = 'Exceeded max lines. Limit: {{ limit }}, Entered: {{ count }}. Remove {{ excess }} lines.';

    /**
     * @return string[]
     */
    public function getRequiredOptions(): array
    {
        return ['maxLines'];
    }

    public function getTargets(): array|string
    {
        return self::CLASS_CONSTRAINT;
    }

    public function validatedBy(): string
    {
        return MaxLinesConstraintValidator::class;
    }
}

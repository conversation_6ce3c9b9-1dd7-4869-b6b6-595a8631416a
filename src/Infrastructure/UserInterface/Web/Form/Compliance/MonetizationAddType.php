<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Compliance;

use Domain\Brand\BrandRepositoryInterface;
use Domain\Monetization\Monetization;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\NotBlank;

final class MonetizationAddType extends AbstractType
{
    public function __construct(
        private readonly BrandRepositoryInterface $brandRepository
    )
    {
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $brands = [];

        foreach ($this->brandRepository->findAll() as $brand) {
            if ($brand->monetization === null) {
                $brands[] = $brand;
            }
        }

        $builder
            ->setErrorBubbling(false)
            ->add(
                'brand',
                ChoiceType::class,
                [
                    'constraints'  => [
                        new NotBlank(),
                    ],
                    'choices'      => $brands,
                    'choice_value' => 'id',
                    'choice_label' => 'slug',
                    'placeholder'  => 'Select a brand',
                    'required'     => true,
                ]
            )
            ->add('adsEnabled')
            ->add('relatedTermsEnabled')
            ->add('displayBannersEnabled')
            ->add('Save', SubmitType::class)
            ->addEventListener(FormEvents::POST_SUBMIT, fn (FormEvent $event) => $this->validateForm($event));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => Monetization::class,
            ]
        );
    }

    private function validateForm(FormEvent $event): void
    {
        $form = $event->getForm();

        if (!$form->isValid()) {
            return;
        }

        /** @var Monetization $monetization */
        $monetization = $form->getData();

        if (!$monetization->allEnabled()) {
            return;
        }

        $form->get('adsEnabled')->addError(
            new FormError('Please disable at least one monetization option')
        );
    }
}

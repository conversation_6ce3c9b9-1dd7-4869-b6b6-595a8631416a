<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Compliance;

use Domain\TrademarkInfringement\TrademarkInfringement;
use Domain\TrademarkInfringement\TrademarkInfringementMatchType;
use Domain\TrademarkInfringement\TrademarkInfringementRepositoryInterface;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\EnumType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Visymo\Shared\Domain\Locale\Locale;

final class TrademarkInfringementType extends AbstractType
{
    public function __construct(
        private readonly TrademarkInfringementRepositoryInterface $trademarkInfringementRepository
    )
    {
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $localeChoices = [];

        foreach (Locale::SUPPORTED_LOCALES as $locale) {
            $localeChoices[$locale] = $locale;
        }

        $builder
            ->add(
                'query',
                TextType::class,
                [
                    'label'       => 'Query',
                    'required'    => true,
                    'attr'        => [
                        'maxlength' => 255,
                    ],
                    'empty_data'  => '',
                    'constraints' => [
                        new Length(['max' => 255]),
                        new NotBlank(),
                    ],
                ]
            )
            ->add(
                'matchType',
                EnumType::class,
                [
                    'label'        => 'Match type',
                    'required'     => true,
                    'class'        => TrademarkInfringementMatchType::class,
                    'choice_label' => static fn (TrademarkInfringementMatchType $matchType) => $matchType->value,
                    'constraints'  => [
                        new NotBlank(),
                    ],
                ]
            )
            ->add(
                'locales',
                ChoiceType::class,
                [
                    'attr'     => [
                        'size' => 10,
                    ],
                    'expanded' => false,
                    'multiple' => true,
                    'choices'  => $localeChoices,
                    'help'     => 'Select no locales to match all',
                ]
            )
            ->add('Save', SubmitType::class)
            ->addEventListener(FormEvents::POST_SUBMIT, fn (FormEvent $event) => $this->validateForm($event));
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => TrademarkInfringement::class,
            ]
        );
    }

    private function validateForm(FormEvent $event): void
    {
        $queryField = $event->getForm()->get('query');

        if (!$queryField->isValid()) {
            return;
        }

        /** @var TrademarkInfringement $trademarkInfringement */
        $trademarkInfringement = $event->getData();

        $compareTrademarkInfringement = $this->trademarkInfringementRepository
            ->findOneByQuery($trademarkInfringement->query);

        if ($compareTrademarkInfringement === null) {
            return;
        }

        if ($trademarkInfringement->hasId() && $trademarkInfringement->id === $compareTrademarkInfringement->id) {
            return;
        }

        $queryField->addError(
            new FormError('A trademark with this query already exists')
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\Info;

use Infrastructure\UserInterface\Web\Request\RequestInterface;

interface RequestInfoInterface extends RequestInterface
{
    public const string KEY_URL                     = 'url';
    public const string KEY_ROUTE                   = 'route';
    public const string KEY_LOCALE                  = 'locale';
    public const string KEY_USER_IP                 = 'user_ip';
    public const string KEY_USER_AGENT              = 'user_agent';
    public const string KEY_USER_AGENT_CLIENT_HINTS = 'user_agent_client_hints';
    public const string KEY_ACCEPT_LANGUAGE         = 'accept_language';
    public const string KEY_HOST                    = 'host';
    public const string KEY_NORMALISED_HOST         = 'normalised_host';
    public const string KEY_HAS_URL_PARAMETERS      = 'has_url_parameters';
    public const string KEY_TLS_VERSION             = 'tls_version';
    public const string KEY_REFERER                 = 'referer';
    public const string KEY_PATH_INFO               = 'path_info';
    public const string KEY_METHOD                  = 'method';
    public const string KEY_SCHEME                  = 'scheme';
    public const string KEY_PROTOCOL                = 'protocol';

    public function getUrl(): string;

    public function getRoute(): string;

    public function isRoute(string $route): bool;

    /**
     * The valid locale set on the request
     *
     * The locale is updated with the domain locale settings from {@see WebsiteSettings}.
     * Don't confuse this value with the `locale` as URL parameter.
     *
     * @see InitLocaleEventSubscriber::setActiveLocale()
     */
    public function getLocale(): string;

    public function getUserIp(): ?string;

    public function getUserAgent(): string;

    /**
     * @return array<string, string>
     */
    public function getUserAgentClientHints(): array;

    public function getAcceptLanguage(): string;

    public function getHost(): string;

    public function hasUrlParameters(): bool;

    public function getTlsVersion(): ?string;

    public function getReferer(): ?string;

    public function getPathInfo(): string;

    public function getMethod(): string;

    public function getScheme(): string;

    public function getProtocol(): string;
}

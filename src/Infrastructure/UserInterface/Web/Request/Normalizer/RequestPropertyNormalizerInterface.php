<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\Normalizer;

interface RequestPropertyNormalizerInterface
{
    public function getString(string $value): ?string;

    public function getBool(?string $value): ?bool;

    public function getInt(int $value): ?int;

    /**
     * @param string[] $value
     *
     * @return string[]|null
     */
    public function getStringArray(array $value): ?array;
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\FlagBag;

use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestParameterBagInterface;

final readonly class RequestFlagBag implements RequestFlagBagInterface
{
    public function __construct(
        private RequestParameterBagInterface $attributesBag
    )
    {
    }

    public function getBool(string $flag): bool
    {
        return $this->attributesBag->getNullableBool($flag) ?? false;
    }
}

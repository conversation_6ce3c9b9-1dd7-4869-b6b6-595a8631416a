<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\ParameterBag;

interface RequestParameterBagInterface
{
    final public const string BOOL_VALUE_YES = 'y';
    final public const string BOOL_VALUE_NO  = 'n';

    final public const array BOOL_VALUE_MAPPING = [
        0                    => false,
        1                    => true,
        self::BOOL_VALUE_NO  => false,
        self::BOOL_VALUE_YES => true,
    ];

    public function isEmpty(): bool;

    /**
     * @return string[] Parameter names, not values
     */
    public function getParameters(): array;

    public function getString(string $parameter): string;

    /**
     * @param string[] $acceptedValues
     */
    public function getAcceptedString(string $parameter, array $acceptedValues): string;

    public function getNullableString(string $parameter): ?string;

    public function getInt(string $parameter): int;

    public function getNullableInt(string $parameter): ?int;

    public function getUnsignedInt(string $parameter): int;

    public function getNullableUnsignedInt(string $parameter): ?int;

    public function getBool(string $parameter): bool;

    public function getNullableBool(string $parameter): ?bool;

    /**
     * @return mixed[]|null
     */
    public function getNullableArray(string $parameter): ?array;

    public function logUnexpected(string $parameter, string $value): void;

    public function logDeprecatedUsage(string $parameter, string $value): void;

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array;
}

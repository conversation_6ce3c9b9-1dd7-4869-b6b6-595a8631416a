<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\ParameterBag;

use Psr\Log\LoggerInterface;

class RequestParameterBag implements RequestParameterBagInterface
{
    /** @var array<string, mixed> */
    protected readonly array $parameters;

    /** @var string[] */
    private array $parameterKeys;

    /**
     * @param array<string, mixed> $parameters
     */
    public function __construct(
        array $parameters,
        private readonly bool $handleParametersCaseSensitive,
        private readonly LoggerInterface $logger
    )
    {
        if ($this->handleParametersCaseSensitive) {
            $this->parameters = $parameters;
        } else {
            $this->parameters = array_change_key_case($parameters, CASE_LOWER);
        }
    }

    public function isEmpty(): bool
    {
        return $this->parameters === [];
    }

    /**
     * @inheritDoc
     */
    public function getParameters(): array
    {
        if (!isset($this->parameterKeys)) {
            $this->parameterKeys = array_keys($this->parameters);
        }

        return $this->parameterKeys;
    }

    public function getString(string $parameter): string
    {
        $value = $this->getScalar($parameter);

        /** @phpstan-ignore-next-line PHPStan is missing the fact $value could be an int */
        $value = is_scalar($value) ? trim((string)$value) : '';
        $value = $this->getUtf8EncodedString($value);

        return $value;
    }

    /**
     * @inheritDoc
     */
    public function getAcceptedString(string $parameter, array $acceptedValues): string
    {
        return $this->getStringIfAccepted(
            $parameter,
            $this->getString($parameter),
            $acceptedValues
        );
    }

    public function getNullableString(string $parameter): ?string
    {
        $value = $this->getString($parameter);

        return $value === '' ? null : $value;
    }

    public function getInt(string $parameter): int
    {
        $value = $this->getNullableInt($parameter);

        return $value ?? 0;
    }

    public function getNullableInt(string $parameter): ?int
    {
        $value = $this->getString($parameter);
        $intValue = (int)$value;

        if ($value === (string)$intValue) {
            return $intValue;
        }

        return null;
    }

    public function getUnsignedInt(string $parameter): int
    {
        $value = $this->getInt($parameter);

        return max($value, 0);
    }

    public function getNullableUnsignedInt(string $parameter): ?int
    {
        $value = $this->getNullableInt($parameter);

        return $value >= 0 ? $value : null;
    }

    public function getBool(string $parameter): bool
    {
        return (bool)$this->getNullableBool($parameter);
    }

    public function getNullableBool(string $parameter): ?bool
    {
        $value = $this->parameters[$parameter] ?? null;

        if (is_bool($value)) {
            return $value;
        }

        $value = $this->getNullableString($parameter);

        if ($value !== null) {
            $lowercaseValue = mb_strtolower($value);

            return self::BOOL_VALUE_MAPPING[$lowercaseValue] ?? null;
        }

        return null;
    }

    /**
     * @inheritDoc
     */
    public function getNullableArray(string $parameter): ?array
    {
        $parameter = $this->normalizeParameter($parameter);

        /** @var mixed[]|null $value */
        $value = $this->parameters[$parameter] ?? null;

        return is_array($value) && $value !== [] ? $value : null;
    }

    public function logUnexpected(string $parameter, string $value): void
    {
        $this->logger->notice(
            'Received unexpected value "{value}" for parameter "{parameter}"',
            [
                'parameter' => $parameter,
                'value'     => $value,
            ]
        );
    }

    public function logDeprecatedUsage(string $parameter, string $value): void
    {
        $this->logger->notice(
            'Deprecated parameter "{parameter}" was used with value "{value}"',
            [
                'parameter' => $parameter,
                'value'     => $value,
            ]
        );
    }

    /**
     * @return array<string, mixed>
     */
    public function toArray(): array
    {
        return $this->parameters;
    }

    protected function normalizeParameter(string $parameter): string
    {
        return $this->handleParametersCaseSensitive ? $parameter : mb_strtolower($parameter);
    }

    protected function getScalar(string $parameter): mixed
    {
        $parameter = $this->normalizeParameter($parameter);
        $value = $this->parameters[$parameter] ?? null;

        return is_scalar($value) ? $value : null;
    }

    /**
     * @param string[] $acceptedValues
     */
    private function getStringIfAccepted(string $parameter, string $value, array $acceptedValues): string
    {
        if ($value !== '') {
            if (in_array($value, $acceptedValues, true)) {
                return $value;
            }

            $this->logUnexpected($parameter, $value);
        }

        return '';
    }

    private function getUtf8EncodedString(string $value): string
    {
        // Fix encoding
        if ($value !== '' && !mb_check_encoding($value, 'UTF-8')) {
            $value = mb_convert_encoding(urldecode($value), 'UTF-8', 'ISO-8859-1');
        }

        return $value;
    }
}

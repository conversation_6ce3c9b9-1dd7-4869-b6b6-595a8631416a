<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\Manager;

use Infrastructure\UserInterface\Web\Request\FlagBag\RequestFlagBagFactory;
use Infrastructure\UserInterface\Web\Request\FlagBag\RequestFlagBagInterface;
use Infrastructure\UserInterface\Web\Request\Main\MainRequestInterface;
use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestHeaderParameterBagFactory;
use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestParameterBagFactory;
use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestParameterBagInterface;

final class RequestManager implements RequestManagerInterface
{
    private ?RequestParameterBagInterface $queryBag = null;

    private ?RequestParameterBagInterface $requestBag = null;

    private ?RequestParameterBagInterface $headersBag = null;

    private ?RequestParameterBagInterface $serverBag = null;

    private ?RequestFlagBagInterface $flagBag = null;

    public function __construct(
        private readonly MainRequestInterface $mainRequest,
        private readonly RequestParameterBagFactory $requestParameterBagFactory,
        private readonly RequestHeaderParameterBagFactory $requestHeaderParameterBagFactory,
        private readonly RequestFlagBagFactory $requestFlagBagFactory
    )
    {
    }

    public function attributesBag(): RequestParameterBagInterface
    {
        // Attributes can be set during the request, so we need to create a new bag every time.
        // Like adding the route attributes
        return $this->requestParameterBagFactory->create(
            parameters: $this->mainRequest->getRequest()->attributes->all()
        );
    }

    public function queryBag(): RequestParameterBagInterface
    {
        if ($this->queryBag === null) {
            $this->queryBag = $this->requestParameterBagFactory->create(
                parameters: $this->mainRequest->getRequest()->query->all()
            );
        }

        return $this->queryBag;
    }

    public function requestBag(): RequestParameterBagInterface
    {
        if ($this->requestBag === null) {
            $this->requestBag = $this->requestParameterBagFactory->create(
                parameters: $this->mainRequest->getRequest()->request->all()
            );
        }

        return $this->requestBag;
    }

    public function cookiesBag(): RequestParameterBagInterface
    {
        // Cookies can be set during the request, so we need to create a new bag every time
        return $this->requestParameterBagFactory->create(
            parameters: $this->mainRequest->getRequest()->cookies->all()
        );
    }

    public function headersBag(): RequestParameterBagInterface
    {
        if ($this->headersBag === null) {
            $this->headersBag = $this->requestHeaderParameterBagFactory->create(
                parameters: $this->mainRequest->getRequest()->headers->all()
            );
        }

        return $this->headersBag;
    }

    public function serverBag(): RequestParameterBagInterface
    {
        if ($this->serverBag === null) {
            $this->serverBag = $this->requestParameterBagFactory->create(
                parameters: $this->mainRequest->getRequest()->server->all()
            );
        }

        return $this->serverBag;
    }

    public function flagBag(): RequestFlagBagInterface
    {
        if ($this->flagBag === null) {
            // The flag bag will be created with the attributes bag once.
            // All flags will be available on the moment this method is called.
            $this->flagBag = $this->requestFlagBagFactory->create(
                attributesBag: $this->attributesBag()
            );
        }

        return $this->flagBag;
    }
}

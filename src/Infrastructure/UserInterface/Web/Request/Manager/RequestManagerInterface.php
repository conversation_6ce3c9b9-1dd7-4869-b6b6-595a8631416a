<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request\Manager;

use Infrastructure\UserInterface\Web\Request\FlagBag\RequestFlagBagInterface;
use Infrastructure\UserInterface\Web\Request\ParameterBag\RequestParameterBagInterface;

interface RequestManagerInterface
{
    public function attributesBag(): RequestParameterBagInterface;

    public function queryBag(): RequestParameterBagInterface;

    public function requestBag(): RequestParameterBagInterface;

    public function cookiesBag(): RequestParameterBagInterface;

    public function headersBag(): RequestParameterBagInterface;

    public function serverBag(): RequestParameterBagInterface;

    public function flagBag(): RequestFlagBagInterface;
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request;

use Domain\Brand\Brand;
use Infrastructure\UserInterface\Web\Request\Manager\RequestManagerInterface;
use Infrastructure\UserInterface\Web\Request\Normalizer\RequestPropertyNormalizerInterface;

final class BrandAssetsRequest implements RequestInterface
{
    public const string PARAMETER_BRAND_SLUGS   = 'brand_slugs';
    public const string PARAMETER_IS_BRAND_PAGE = 'is_brand_page';

    /** @var string[] */
    private array $brandSlugs;

    private bool $isBrandPage;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    /**
     * @return string[]|null
     */
    public function getBrandSlugs(): ?array
    {
        if (!isset($this->brandSlugs)) {
            $slugs = $this->requestManager->queryBag()->getString(self::PARAMETER_BRAND_SLUGS);
            $slugs = explode(',', $slugs);
            $slugs = array_filter(
                $slugs,
                static fn (string $slug): bool => preg_match(sprintf('~%s~', Brand::SLUG_REGEX), $slug) === 1
            );
            $slugs = array_unique($slugs);
            $this->brandSlugs = array_values($slugs);
        }

        return $this->requestPropertyNormalizer->getStringArray($this->brandSlugs);
    }

    public function isBrandPage(): bool
    {
        if (!isset($this->isBrandPage)) {
            $this->isBrandPage = $this->requestManager->queryBag()->getBool(self::PARAMETER_IS_BRAND_PAGE);
        }

        return $this->isBrandPage;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_BRAND_SLUGS => $this->getBrandSlugs(),
        ];
    }
}

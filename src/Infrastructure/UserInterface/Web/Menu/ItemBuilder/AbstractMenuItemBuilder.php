<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Menu\ItemBuilder;

use Infrastructure\UserInterface\Web\Menu\Model\MenuItem;
use Infrastructure\UserInterface\Web\Menu\Model\MenuItemFactory;
use Infrastructure\UserInterface\Web\Menu\Model\SubmenuItem;
use Infrastructure\UserInterface\Web\Menu\Model\SubmenuItemFactory;
use Infrastructure\UserInterface\Web\Security\Helper\UserHelperInterface;
use Infrastructure\UserInterface\Web\Security\SecurityChecker;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\Routing\RouterInterface;

abstract readonly class AbstractMenuItemBuilder implements MenuItemBuilderInterface
{
    public function __construct(
        protected UserHelperInterface $userHelper,
        private MenuItemFactory $menuItemFactory,
        private SubmenuItemFactory $submenuItemFactory,
        private RequestStack $requestStack,
        private RouterInterface $router,
        private SecurityChecker $securityChecker
    )
    {
    }

    /**
     * @param string[]                $activateOnRoutes
     * @param array<SubmenuItem|null> $submenuItems
     */
    protected function createMenuItem(
        string $text,
        string $icon,
        string $route,
        array $activateOnRoutes = [],
        array $submenuItems = []
    ): ?MenuItem
    {
        if (!$this->securityChecker->isGrantedForRoute($route)) {
            return null;
        }

        $submenuItems = array_filter(
            $submenuItems,
            static fn (?SubmenuItem $submenuItem): bool => $submenuItem !== null
        );

        $activateOnRoutes[] = $route;
        $active = $this->isActive($activateOnRoutes);
        $url = $this->router->generate($route);

        return $this->menuItemFactory->create(
            text        : $text,
            icon        : $icon,
            url         : $url,
            active      : $active,
            submenuItems: $submenuItems
        );
    }

    /**
     * @param string[] $activateOnRoutes
     */
    protected function createSubmenuItem(
        string $text,
        string $route,
        array $activateOnRoutes = []
    ): ?SubmenuItem
    {
        if (!$this->securityChecker->isGrantedForRoute($route)) {
            return null;
        }

        $activateOnRoutes[] = $route;
        $active = $this->isActive($activateOnRoutes);
        $url = $this->router->generate($route);

        return $this->submenuItemFactory->create(
            text  : $text,
            url   : $url,
            active: $active
        );
    }

    /**
     * @param string[] $activateOnRoutes
     */
    private function isActive(array $activateOnRoutes): bool
    {
        $currentRoute = $this->getCurrentRoute();

        foreach ($activateOnRoutes as $route) {
            if ($currentRoute === $route) {
                return true;
            }

            if (!str_contains($route, '*')) {
                continue;
            }

            // Wildcard
            $routePattern = sprintf(
                '/^%s$/',
                str_replace('*', '.*', $route)
            );

            if (preg_match($routePattern, $currentRoute) === 1) {
                return true;
            }
        }

        return false;
    }

    private function getCurrentRoute(): string
    {
        /** @var Request $mainRequest */
        $mainRequest = $this->requestStack->getMainRequest();

        /** @phpstan-ignore-next-line Cannot cast mixed to string. */
        return (string)$mainRequest->get('_route');
    }
}

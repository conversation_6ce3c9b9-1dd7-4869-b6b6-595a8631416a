<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Menu\ItemBuilder;

use Infrastructure\UserInterface\Web\Menu\Model\MenuItem;

final readonly class AuthenticateMenuItemBuilder extends AbstractMenuItemBuilder
{
    public static function priority(): int
    {
        return 0;
    }

    public function create(): ?MenuItem
    {
        if (!$this->userHelper->isUserLoggedIn()) {
            return $this->createMenuItem(
                'Login',
                'box-arrow-in-right',
                'saml_login',
            );
        }

        return $this->createMenuItem(
            'Logout',
            'box-arrow-left',
            'saml_logout',
        );
    }
}

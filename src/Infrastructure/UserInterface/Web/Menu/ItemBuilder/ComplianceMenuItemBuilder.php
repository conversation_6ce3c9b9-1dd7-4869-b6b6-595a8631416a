<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Menu\ItemBuilder;

use Infrastructure\UserInterface\Web\Menu\Model\MenuItem;

final readonly class ComplianceMenuItemBuilder extends AbstractMenuItemBuilder
{
    public static function priority(): int
    {
        return 100;
    }

    public function create(): ?MenuItem
    {
        return $this->createMenuItem(
            text            : 'Compliance',
            icon            : 'shield-shaded',
            route           : 'route_compliance_dashboard',
            activateOnRoutes: ['route_compliance_*'],
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class DeployExtension extends AbstractExtension
{
    /**
     * @inheritDoc
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'get_gitlab_compare_build_url',
                $this->getGitLabCompareBuildUrl(...),
                ['is_safe' => ['html']]
            ),
            new TwigFunction(
                'get_gitlab_project_url',
                $this->getGitLabProjectUrl(...),
                ['is_safe' => ['html']]
            ),
        ];
    }

    private function getGitLabCompareBuildUrl(string $project, string $buildVersion): string
    {
        return sprintf(
            '%s/-/compare/%s...master?view=parallel',
            $this->getGitLabProjectUrl($project),
            $buildVersion
        );
    }

    private function getGitLabProjectUrl(string $project): string
    {
        return sprintf(
            'https://git.visymo.com/visymo/developers/serp-frontend/%s',
            $project
        );
    }
}

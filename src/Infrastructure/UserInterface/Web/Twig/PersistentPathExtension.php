<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Twig;

use Infrastructure\UserInterface\Web\Request\Info\RequestInfoInterface;
use Infrastructure\UserInterface\Web\Url\PersistentUrlParametersHelper;
use Infrastructure\UserInterface\Web\Url\PersistentUrlParametersRouter;
use Twig\Environment;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class PersistentPathExtension extends AbstractExtension
{
    public function __construct(
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly PersistentUrlParametersHelper $persistentUrlParametersHelper,
        private readonly RequestInfoInterface $requestInfo,
        private readonly Environment $twig
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'persistent_path',
                $this->generatePersistentPath(...),
            ),
            new TwigFunction(
                'persistent_path_current_route',
                $this->generatePersistentPathCurrentRoute(...),
            ),
            new TwigFunction(
                'persistent_path_input_fields',
                $this->getPersistentPathInputFields(...),
                ['is_safe' => ['html']]
            ),
        ];
    }

    /**
     * @param array<string, mixed> $parameters
     */
    public function generatePersistentPath(
        string $route,
        array $parameters = [],
        bool $absoluteUrl = false
    ): string
    {
        return $this->persistentUrlParametersRouter->generate($route, $parameters, $absoluteUrl);
    }

    /**
     * @param array<string, mixed> $parameters
     */
    public function generatePersistentPathCurrentRoute(array $parameters = []): string
    {
        $route = $this->requestInfo->getRoute();

        return $this->generatePersistentPath($route, $parameters, true);
    }

    /**
     * @param array<string, string|null> $exceptParameters
     */
    public function getPersistentPathInputFields(
        array $exceptParameters = []
    ): string
    {
        $inputFields = [...$this->persistentUrlParametersHelper->getPersistentParameters()];
        $inputFields = $this->arrayRemoveNullValues($inputFields);

        foreach ($exceptParameters as $parameter) {
            unset($inputFields[$parameter]);
        }

        return $this->twig->render(
            'form/_persistent_path_input_fields.html.twig',
            [
                'inputFields' => $inputFields,
            ],
        );
    }

    /**
     * @param mixed[] $array
     *
     * @return mixed[]
     */
    private function arrayRemoveNullValues(array $array): array
    {
        return array_filter($array, static fn (?string $value) => $value !== null);
    }
}

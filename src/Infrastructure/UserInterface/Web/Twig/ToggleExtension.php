<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Twig;

use Infrastructure\UserInterface\Web\Request\ToggleRequest;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class ToggleExtension extends AbstractExtension
{
    public function __construct(
        private readonly ToggleRequest $toggleRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'is_toggle_active',
                $this->toggleRequest->isActive(...)
            ),
        ];
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\Table;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/Table/table.html.twig')]
final class Table implements ComponentInterface
{
    public ?string $searchTarget = null;

    public function getComponentCssClass(): string
    {
        return 'table';
    }
}

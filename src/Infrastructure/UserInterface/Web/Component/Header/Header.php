<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\Header;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/Header/header.html.twig')]
final class Header implements ComponentInterface
{
    public string $title;

    public function getComponentCssClass(): string
    {
        return 'header';
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\JsonEditor;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/JsonEditor/json_editor.html.twig')]
final class JsonEditor implements ComponentInterface
{
    public string $schemaUrl;

    public function getComponentCssClass(): string
    {
        return 'json-editor';
    }
}

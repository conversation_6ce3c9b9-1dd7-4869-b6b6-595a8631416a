/** @define select-filter */
.select-filter {
    display: inline-block;
    position: relative;

    &:focus-within {
        --indicator_rotate: 180deg;
    }

    &__indicator {
        color: #ffffff;
        font-size: 1.6rem;
        position: absolute;
        right: 1rem;
        rotate: 0deg;
        top: 50%;
        transform: translateY(-50%) rotate(var(--indicator_rotate, 0deg));
        transform-origin: center;
        transition: transform 0.2s ease-in-out;
    }

    &__select {
        appearance: none;
        background-color: var(--select_background-color, #0d6efd);
        border: none;
        border-collapse: separate;
        border-radius: 0.6rem;
        color: #ffffff;
        cursor: pointer;
        font-size: 1.6rem;
        height: 3.8rem;
        line-height: 3.8rem;
        outline: 0;
        padding: 0 2rem 0 1rem;
        text-align: center;
        white-space: nowrap;
    }

    &__option {
        background-color: var(--select-option_background-color, #ffffff);
        color: var(--select-option_color, #000000);
        text-align: left;
    }
}

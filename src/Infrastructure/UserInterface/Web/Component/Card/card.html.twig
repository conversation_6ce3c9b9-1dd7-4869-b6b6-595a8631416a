{% if foldable %}
    {{ component_script('card') }}
{% endif %}
{{ component_style('card') }}
{% if modifiers is not null %}
    {% set modifier_class_prefix = ' ' ~ this.getComponentCssClass() ~ '--' %}
    {% set modifiers = modifier_class_prefix ~ modifiers|split(' ')|join(modifier_class_prefix) %}
{% else %}
    {% set modifiers = '' %}
{% endif %}

<div class="{{ this.getComponentCssClass }}{{ foldable ? ' ' ~ this.getComponentCssClass ~ '--foldable' }}{{ modifiers }}">
    {% if title is not null %}
        <h2 class="{{ this.getComponentCssClass() }}__title">{{ title }}</h2>
    {% endif %}
    <div class="{{ this.getComponentCssClass() }}__content">
        {% block content %}{% endblock %}
    </div>
</div>

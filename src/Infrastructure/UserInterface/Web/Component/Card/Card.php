<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\Card;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/Card/card.html.twig')]
final class Card implements ComponentInterface
{
    public ?string $title;

    public bool $foldable;

    public ?string $modifiers = null;

    public function mount(?string $title = null, bool $foldable = false): void
    {
        $title = trim((string)$title);
        $title = $title !== '' ? $title : null;

        if ($foldable && $title === null) {
            throw new \InvalidArgumentException('A foldable card must have a title.');
        }

        $this->title = $title;
        $this->foldable = $foldable;
    }

    public function getComponentCssClass(): string
    {
        return 'card';
    }
}

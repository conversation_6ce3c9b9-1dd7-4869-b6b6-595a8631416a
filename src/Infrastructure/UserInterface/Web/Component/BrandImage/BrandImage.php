<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\BrandImage;

use Domain\BrandAssets\BrandAssets;
use Domain\BrandAssets\Exception\BrandAssetsImageNotFoundException;
use Domain\BrandAssets\Image\BrandAssetsImageFileName;
use Infrastructure\Service\BrandAssets\Image\BrandAssetsImage;
use Infrastructure\Service\BrandAssets\Image\BrandAssetsImageFactory;
use Infrastructure\Service\BrandAssets\Image\Exception\BrandAssetsInvalidImageSizeException;
use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/BrandImage/brand_image.html.twig')]
final class BrandImage implements ComponentInterface
{
    public BrandAssets $brandAssets;

    public string $imageName;

    public ?BrandAssetsImage $image = null;

    public ?BrandAssetsImage $compareImage = null;

    public ?string $error = null;

    public ?string $modifiers = null;

    public function __construct(
        private readonly BrandAssetsImageFactory $brandAssetsImageFactory
    )
    {
    }

    public function mount(
        BrandAssets $brandAssets,
        string $imageName,
        ?string $imageCompareName = null,
        int $imageCompareMultiplier = 1
    ): void
    {
        $this->brandAssets = $brandAssets;
        $this->imageName = $imageName;

        // Image
        try {
            $fileName = BrandAssetsImageFileName::from($imageName);
            $content = $brandAssets->getImageContent(
                imageFileName: $fileName
            );

            $this->image = $this->brandAssetsImageFactory->createFromValue($content, $imageName);
        } catch (BrandAssetsImageNotFoundException) {
            // Expected
        } catch (\Throwable $exception) {
            $this->error = $exception->getMessage();
        }

        // Compare with image
        if ($this->image !== null && $imageCompareName !== null) {
            try {
                $fileName = BrandAssetsImageFileName::from($imageCompareName);
                $content = $brandAssets->getImageContent(
                    imageFileName: $fileName
                );
                $this->compareImage = $this->brandAssetsImageFactory->createFromValue($content, $imageCompareName);
            } catch (\Throwable $exception) {
                $this->error = $exception->getMessage();
            }

            try {
                $this->compareImage?->matchesDimensionsOfImage($this->image, $imageCompareMultiplier);
            } catch (BrandAssetsInvalidImageSizeException $exception) {
                $multiplierMessage = $imageCompareMultiplier > 1
                    ? sprintf(' multiplied by %u', $imageCompareMultiplier)
                    : '';
                $this->error = sprintf(
                    'Image size of "%s"%s compared to "%s" is invalid: %s',
                    $imageCompareName,
                    $multiplierMessage,
                    $imageName,
                    $exception->expectedSizeMessage
                );
            }
        }
    }

    public function getComponentCssClass(): string
    {
        return 'brand-image';
    }
}

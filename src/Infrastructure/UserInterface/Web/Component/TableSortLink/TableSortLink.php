<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\TableSortLink;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Infrastructure\UserInterface\Web\Request\SortRequest;
use Infrastructure\UserInterface\Web\Url\PersistentUrlParametersProviderInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/TableSortLink/table_sort_link.html.twig')]
final class TableSortLink implements ComponentInterface, PersistentUrlParametersProviderInterface
{
    public string $sort;

    public ?string $parameters = null;

    public function __construct(
        private readonly SortRequest $sortRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getPersistentUrlParameters(): array
    {
        $sort = $this->sort ?? '';
        $parameters = [
            SortRequest::PARAMETER_SORT           => $sort,
            SortRequest::PARAMETER_SORT_DIRECTION => $this->sortRequest->getDirectionForColumn($sort),
        ];

        if ($this->parameters !== null) {
            $parameters = [
                ...json_decode($this->parameters, true, 512, JSON_THROW_ON_ERROR),
                ...$parameters,
            ];
        }

        return $parameters;
    }

    public function getComponentCssClass(): string
    {
        return 'table-sort-link';
    }
}

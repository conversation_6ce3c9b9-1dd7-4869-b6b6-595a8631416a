<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\ColorBox;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/ColorBox/colorBox.html.twig')]
final class ColorBox implements ComponentInterface
{
    public string $color;

    public function getComponentCssClass(): string
    {
        return 'color-box';
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\Tag;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/Tag/tag.html.twig')]
final class Tag implements ComponentInterface
{
    public ?string $key = null;

    public string $value;

    public function getComponentCssClass(): string
    {
        return 'tag';
    }
}

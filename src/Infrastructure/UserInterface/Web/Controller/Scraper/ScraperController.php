<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\Scraper;

use Application\Command\Scraper\AbortScraperBatch\AbortScraperBatchCommand;
use Application\Command\Scraper\RetryScraperBatch\RetryScraperBatchCommand;
use Domain\Scraper\ScraperBatch\ScraperBatch;
use Domain\Scraper\ScraperBatch\ScraperBatchRepositoryInterface;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlRepositoryInterface;
use Domain\User\Security\SecurityRole;
use Infrastructure\Service\DataFilter\Collection\DataFilterCollection;
use Infrastructure\Service\DataFilter\Matcher\DataFilterMatcherFactory;
use Infrastructure\Service\ScraperBatch\ScraperBatchInitializer;
use Infrastructure\Service\ScraperBatch\ScraperBatchKeywordsCleaner;
use Infrastructure\UserInterface\Web\FlashMessage\FlashMessageRepository;
use Infrastructure\UserInterface\Web\Form\Scraper\ScraperBatchType;
use Infrastructure\UserInterface\Web\Request\DataFilterRequest;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Visymo\CommandBus\Domain\CommandBus\DirectCommandBusInterface;

final class ScraperController extends AbstractController
{
    public function __construct(
        private readonly ScraperBatchRepositoryInterface $scraperBatchRepository,
        private readonly ScraperBatchInitializer $scraperBatchInitializer,
        private readonly ScraperBatchKeywordsCleaner $scraperBatchKeywordsCleaner,
        private readonly DirectCommandBusInterface $directCommandBus,
        private readonly DataFilterRequest $dataFilterRequest,
        private readonly DataFilterMatcherFactory $dataFilterMatcherFactory,
        private readonly FlashMessageRepository $flashMessageRepository
    )
    {
    }

    #[Route(
        path   : '/scraper/list',
        name   : 'route_scraper_list',
        methods: ['GET']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function list(): Response
    {
        $dataFilterMatcher = $this->dataFilterMatcherFactory->create(
            expression: $this->dataFilterRequest->getExpression(),
            collection: DataFilterCollection::SCRAPER_BATCH,
        );

        $batches = $this->scraperBatchRepository->findAll(100);
        $batches = $dataFilterMatcher->filter($batches);

        return $this->render(
            'scraper/list.html.twig',
            [
                'batches'             => $batches,
                'data_filter_matcher' => $dataFilterMatcher,
            ]
        );
    }

    #[Route(
        path   : '/scraper/view/{id}',
        name   : 'route_scraper_view',
        methods: ['GET']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function viewScraperBatch(
        ScraperBatch $scraperBatch,
        ScraperBatchUrlRepositoryInterface $scraperBatchUrlRepository
    ): JsonResponse
    {
        $scraperBatchUrls = [];

        foreach ($scraperBatchUrlRepository->findByScraperBatchId($scraperBatch->id) as $scraperBatchUrl) {
            $scraperBatchUrls[] = $scraperBatchUrl->toArray();
        }

        return new JsonResponse([$scraperBatch->toArray(), $scraperBatchUrls]);
    }

    #[Route(
        path: '/scraper/create',
        name: 'route_scraper_create'
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function create(Request $request): Response
    {
        $form = $this->createForm(ScraperBatchType::class);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $brand = $form->get('brand')->getData();
            $locale = $form->get('locale')->getData();
            $keywords = $this->scraperBatchKeywordsCleaner->cleanFromString($form->get('keywords')->getData());

            $this->scraperBatchInitializer->createBatchAndScheduleScraping(
                scraper        : $form->get('scraper')->getData(),
                keywords       : $keywords,
                locale         : $locale,
                brandSlug      : $brand,
                searchRoute    : $form->get('searchRoute')->getData(),
                templateVariant: $form->get('templateVariant')->getData(),
                description    : $form->get('description')->getData()
            );

            $this->flashMessageRepository->addSuccess('Scraper batch has been scheduled');

            return $this->redirectToRoute('route_scraper_list');
        }

        return $this->render(
            'scraper/create.html.twig',
            [
                'form' => $form,
            ]
        );
    }

    #[Route(
        path   : '/scraper/view/{id}/abort',
        name   : 'route_scraper_abort',
        methods: ['GET']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function abortScraperBatch(ScraperBatch $scraperBatch): Response
    {
        $this->directCommandBus->handle(
            new AbortScraperBatchCommand(
                id: $scraperBatch->id
            )
        );

        return $this->redirectToRoute('route_scraper_list');
    }

    #[Route(
        path   : '/scraper/view/{id}/retry',
        name   : 'route_scraper_retry',
        methods: ['GET']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function retryScraperBatch(ScraperBatch $scraperBatch): Response
    {
        $this->directCommandBus->handle(
            new RetryScraperBatchCommand(
                id: $scraperBatch->id
            )
        );

        $this->flashMessageRepository->addSuccess('Scraper batch has been scheduled for retry');

        return $this->redirectToRoute('route_scraper_list');
    }

    #[Route(
        path   : '/scraper/view/{id}/delete',
        name   : 'route_scraper_delete',
        methods: ['GET']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function deleteScraperBatch(ScraperBatch $scraperBatch): Response
    {
        $this->scraperBatchRepository->delete($scraperBatch);

        $this->flashMessageRepository->addSuccess('Scraper batch has been deleted');

        return $this->redirectToRoute('route_scraper_list');
    }
}

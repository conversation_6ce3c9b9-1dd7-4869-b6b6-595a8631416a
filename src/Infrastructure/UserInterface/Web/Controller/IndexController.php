<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\Attribute\Route;

final class IndexController extends AbstractController
{
    #[Route(
        path   : '/',
        name   : 'route_home',
        methods: ['GET']
    )]
    public function index(): RedirectResponse
    {
        return $this->redirectToRoute('route_brand_index');
    }
}

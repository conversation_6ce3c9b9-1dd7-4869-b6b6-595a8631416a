<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\Api;

use Application\Query\GetScraperBatchKeywords\GetScraperBatchKeywordsQuery;
use Application\Query\GetScraperBatchKeywords\GetScraperBatchKeywordsResponse;
use Domain\Scraper\Exception\ScraperBatchValidationFailedException;
use Domain\Scraper\ScraperBatch\Exception\ScraperBatchNotFoundException;
use Domain\Scraper\ScraperBatch\ScraperBatchRepositoryInterface;
use Domain\Scraper\ScraperRepositoryInterface;
use Domain\SearchRoute\SearchRoute;
use Infrastructure\Service\ScraperBatch\ScraperBatchInitializer;
use Infrastructure\Service\ScraperBatch\ScraperBatchValidator;
use Infrastructure\UserInterface\Web\Request\JsonRpc2\JsonRpcRequestFactory;
use Infrastructure\UserInterface\Web\Request\ScraperBatchRequest;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\QueryBus\QueryBus\QueryBusInterface;

final class ApiScraperController extends AbstractController
{
    public function __construct(
        private readonly QueryBusInterface $queryBus,
        private readonly ScraperRepositoryInterface $scraperRepository,
        private readonly ScraperBatchRepositoryInterface $scraperBatchRepository,
        private readonly ScraperBatchValidator $scraperBatchValidator,
        private readonly JsonRpcRequestFactory $jsonRpcRequestFactory,
        private readonly ScraperBatchInitializer $scraperBatchInitializer
    )
    {
    }

    #[Route(
        path   : '/api/scraper/create',
        name   : 'route_api_scraper_create',
        methods: ['POST'],
    )]
    public function createScraperBatch(): JsonResponse
    {
        $scraperBatchRequest = new ScraperBatchRequest(
            $this->jsonRpcRequestFactory->create(),
        );

        try {
            $this->scraperBatchValidator->validateRequest($scraperBatchRequest);

            $defaultScraper = $this->scraperRepository->findDefault();

            if ($defaultScraper === null) {
                return new JsonResponse(
                    [
                        'message' => 'Could not get default scraper, found no scraper declared as default.',
                    ],
                    Response::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        } catch (ScraperBatchValidationFailedException $exception) {
            return new JsonResponse(
                [
                    'message' => $exception->getMessage(),
                    'errors'  => $exception->getErrors(),
                ],
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        $scraperBatch = $this->scraperBatchInitializer->createBatchAndScheduleScraping(
            scraper        : $defaultScraper,
            keywords       : $scraperBatchRequest->getKeywords(),
            locale         : $scraperBatchRequest->getLocale(),
            brandSlug      : $scraperBatchRequest->getBrandSlug(),
            searchRoute    : SearchRoute::DISPLAY_SEARCH_RELATED,
            templateVariant: $scraperBatchRequest->getTemplateVariant(),
        );

        return new JsonResponse(['scraper_batch_id' => $scraperBatch->id]);
    }

    #[Route(
        path        : '/api/scraper/{batchId}/status',
        name        : 'route_api_scraper_status',
        requirements: [
            'batchId' => '\d+',
        ],
        methods     : ['GET'],
    )]
    public function getScraperBatchStatus(int $batchId): JsonResponse
    {
        try {
            $scraperBatch = $this->scraperBatchRepository->findOneById($batchId);
        } catch (ScraperBatchNotFoundException) {
            return new JsonResponse(
                [
                    'message' => 'Scraper batch not found',
                ],
                Response::HTTP_NOT_FOUND
            );
        }

        $status = $scraperBatch->status->value;

        if ($scraperBatch->status->isStatusTransitional()) {
            $status = 'in-progress';
        }

        /** @var GetScraperBatchKeywordsResponse $response */
        $response = $this->queryBus->handle(
            new GetScraperBatchKeywordsQuery(
                $scraperBatch->id
            )
        );

        return new JsonResponse(
            [
                'status'                   => $status,
                'keywords_with_related'    => $response->keywordsWithRelated,
                'keywords_without_related' => $response->keywordsWithoutRelated,
            ],
            Response::HTTP_OK
        );
    }
}

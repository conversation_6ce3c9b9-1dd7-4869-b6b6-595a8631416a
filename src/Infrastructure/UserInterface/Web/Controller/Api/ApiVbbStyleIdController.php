<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\Api;

use Application\Command\StyleId\UpdateVbbStylesForBrand\UpdateVbbStylesForBrandCommand;
use Domain\Brand\BrandRepositoryInterface;
use Domain\Brand\Exception\BrandNotFoundException;
use Domain\VbbStyleId\Exception\VbbStyleIdFailingValidationException;
use Domain\VbbStyleId\VbbStyleIdValidatorInterface;
use Infrastructure\UserInterface\Web\Request\VbbStyleIdRequest;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;

final class ApiVbbStyleIdController extends AbstractController
{
    public function __construct(
        private readonly VbbStyleIdValidatorInterface $vbbStyleIdValidator,
        private readonly BrandRepositoryInterface $brandRepository,
        private readonly CommandBusInterface $commandBus
    )
    {
    }

    #[Route(
        path   : '/api/vbb-style-id/validate',
        name   : 'route_api_vbb_style_id_validate',
        methods: ['POST']
    )]
    public function validate(VbbStyleIdRequest $vbbStyleIdRequest): Response
    {
        try {
            $this->vbbStyleIdValidator->validateRequest($vbbStyleIdRequest->toArray());
        } catch (\Throwable $exception) {
            return $this->errorResponse([$exception->getMessage()]);
        }

        $onlineStyleIds = $vbbStyleIdRequest->getOnlineStyleIds();

        try {
            $this->vbbStyleIdValidator->validate($onlineStyleIds);
        } catch (VbbStyleIdFailingValidationException $exception) {
            return $this->errorResponse($exception->messages);
        }

        return new JsonResponse([], Response::HTTP_OK);
    }

    #[Route(
        path        : '/api/vbb-style-id/{force?}',
        name        : 'route_api_vbb_style_id',
        requirements: [
            'force' => 'force',
        ],
        methods     : ['POST']
    )]
    public function index(?string $force, VbbStyleIdRequest $vbbStyleIdRequest): Response
    {
        try {
            $this->vbbStyleIdValidator->validateRequest($vbbStyleIdRequest->toArray());
        } catch (\Throwable $exception) {
            return $this->errorResponse([$exception->getMessage()]);
        }

        $disableValidation = $force === 'force';

        try {
            foreach ($vbbStyleIdRequest->getOnline() as $brandSlug => $styleIds) {
                $styleIds = array_map(static fn (int $styleId): string => (string)$styleId, $styleIds);
                $brand = $this->brandRepository->findOneBySlug($brandSlug);

                if ($brand === null) {
                    throw BrandNotFoundException::createForSlug($brandSlug);
                }

                if (!$disableValidation) {
                    $this->vbbStyleIdValidator->validate($styleIds);
                }

                $this->commandBus->handle(
                    new UpdateVbbStylesForBrandCommand(
                        slug    : $brand->slug,
                        styleIds: $styleIds,
                    )
                );
            }

            return new JsonResponse([], Response::HTTP_OK);
        } catch (\Throwable $exception) {
            return $this->errorResponse([$exception->getMessage()]);
        }
    }

    /**
     * @param string[] $messages
     */
    private function errorResponse(array $messages): JsonResponse
    {
        return new JsonResponse(
            ['error' => $messages],
            Response::HTTP_UNPROCESSABLE_ENTITY
        );
    }
}

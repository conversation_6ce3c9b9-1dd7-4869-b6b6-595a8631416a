<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\BrandAssets;

use Application\Command\Brand\PushBrandAssets\PushBrandAssetsCommand;
use Domain\Brand\Brand;
use Domain\Brand\BrandRepositoryInterface;
use Domain\Brand\Exception\BrandNotFoundException;
use Domain\BrandAssets\BrandAssetsFactory;
use Domain\BrandAssets\BrandAssetsRepositoryInterface;
use Domain\User\Security\SecurityRole;
use Infrastructure\Service\BrandAssets\Generator\BrandAssetsChecksumGenerator;
use Infrastructure\Service\BrandAssets\Generator\BrandAssetsConfigGenerator;
use Infrastructure\UserInterface\Web\FlashMessage\FlashMessageRepository;
use Infrastructure\UserInterface\Web\Form\BrandAssets\BrandAssetsPreviewType;
use Infrastructure\UserInterface\Web\Form\BrandAssets\BrandAssetsUploadFileType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;

class BrandAssetsEditController extends AbstractController
{
    public function __construct(
        private readonly BrandAssetsFactory $brandAssetsFactory,
        private readonly BrandRepositoryInterface $brandRepository,
        private readonly BrandAssetsRepositoryInterface $brandAssetsRepository,
        private readonly BrandAssetsConfigGenerator $brandAssetsConfigGenerator,
        private readonly BrandAssetsChecksumGenerator $brandAssetsChecksumGenerator,
        private readonly CommandBusInterface $commandBus,
        private readonly FlashMessageRepository $flashMessageRepository
    )
    {
    }

    #[Route(
        path        : '/brand-assets/brand/{slug}/edit',
        name        : 'route_brand_assets_edit',
        requirements: ['slug' => Brand::SLUG_REGEX],
        methods     : ['GET', 'POST']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function editBrand(string $slug, Request $request): Response
    {
        $brand = $this->brandRepository->findOneBySlug($slug);

        if ($brand === null) {
            throw BrandNotFoundException::createForSlug($slug);
        }

        $form = $this
            ->createForm(BrandAssetsUploadFileType::class)
            ->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                /** @var UploadedFile $zipFile */
                $zipFile = $form->get(BrandAssetsUploadFileType::FIELD_ZIP_FILE)->getData();
                $config = $this->brandAssetsConfigGenerator->generateFromZipFile($zipFile->getRealPath());

                return $this->previewBrandAssets($brand, $config);
            } catch (\Throwable $exception) {
                $form->get(BrandAssetsUploadFileType::FIELD_ZIP_FILE)->addError(
                    new FormError(
                        sprintf(
                            'An error occurred while processing the uploaded file: %s',
                            $exception->getMessage()
                        )
                    )
                );
            }
        }

        return $this->render(
            'brand_assets/edit.html.twig',
            [
                'brand' => $brand,
                'form'  => $form,
            ]
        );
    }

    #[Route(
        path        : '/brand-assets/brand/{slug}/store',
        name        : 'route_brand_assets_store',
        requirements: ['slug' => Brand::SLUG_REGEX],
        methods     : ['GET', 'POST']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function storeBrand(string $slug, Request $request): Response
    {
        $brandAssets = $this->brandAssetsRepository->findOneBySlug($slug);

        if ($brandAssets === null) {
            $brand = $this->brandRepository->findOneBySlug($slug);

            if ($brand === null) {
                throw BrandNotFoundException::createForSlug($slug);
            }

            $brandAssets = $this->brandAssetsFactory->create($brand, []);
        }

        $form = $this->createForm(BrandAssetsPreviewType::class);
        $form->handleRequest($request);

        // Store config
        $brandAssets->setConfigFromJson($form->get(BrandAssetsPreviewType::FIELD_CONFIG)->getData());
        $this->brandAssetsRepository->store($brandAssets);

        // Push assets
        $this->commandBus->handle(
            new PushBrandAssetsCommand($slug)
        );

        $this->flashMessageRepository->addSuccess('Brand assets have been stored');

        return $this->redirectToRoute('route_brand_assets', ['slug' => $slug]);
    }

    /**
     * @param mixed[] $config
     */
    private function previewBrandAssets(Brand $brand, array $config): Response
    {
        $brandAssets = $this->brandAssetsFactory->create(
            brand : $brand,
            config: $config
        );

        $formAction = $this->generateUrl('route_brand_assets_store', ['slug' => $brand->slug]);
        $brandAssetsConfig = $brandAssets->getConfigAsJson();
        $brandAssetsChecksum = $this->brandAssetsChecksumGenerator->generate($brandAssetsConfig);

        $form = $this->createForm(
            BrandAssetsPreviewType::class,
            [
                BrandAssetsPreviewType::FIELD_CONFIG   => $brandAssetsConfig,
                BrandAssetsPreviewType::FIELD_CHECKSUM => $brandAssetsChecksum,
            ],
            [
                'action' => $formAction,
                'method' => 'POST',
            ]
        );

        return $this->render(
            'brand_assets/preview.html.twig',
            [
                'brand'        => $brand,
                'brand_assets' => $brandAssets,
                'form'         => $form,
            ]
        );
    }
}

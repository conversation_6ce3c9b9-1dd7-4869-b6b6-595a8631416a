<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Assets;

final class AssetsRenderedRegistry
{
    /** @var array<string, bool> */
    private array $renderedComponentStyles = [];

    /** @var array<string, bool> */
    private array $renderedComponentScripts = [];

    public function isComponentStyleRendered(string $styleName): bool
    {
        return array_key_exists($styleName, $this->renderedComponentStyles);
    }

    public function registerComponentStyleRender(string $componentType): void
    {
        $this->renderedComponentStyles[$componentType] = true;
    }

    public function isComponentScriptRendered(string $scriptName): bool
    {
        return array_key_exists($scriptName, $this->renderedComponentScripts);
    }

    public function registerComponentScriptRender(string $componentType): void
    {
        $this->renderedComponentScripts[$componentType] = true;
    }
}

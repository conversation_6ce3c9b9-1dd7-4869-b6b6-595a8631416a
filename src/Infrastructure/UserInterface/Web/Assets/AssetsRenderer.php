<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Assets;

use Psr\Log\LoggerInterface;
use Symfony\Component\String\UnicodeString;

final class AssetsRenderer
{
    private bool $renderAssets = true;

    public function __construct(
        private readonly string $buildDirectory,
        private readonly AssetsRenderedRegistry $assetsRenderedRegistry,
        private readonly LoggerInterface $logger
    )
    {
    }

    public function setRenderAssets(bool $renderAssets = true): void
    {
        $this->renderAssets = $renderAssets;
    }

    public function renderEntryStyle(string $entryName): string
    {
        if (!$this->renderAssets) {
            return '';
        }

        $filePath = sprintf('%s/css/entry/%s.css', $this->buildDirectory, $entryName);
        $fileContents = $this->getFileContents($filePath);

        return $fileContents !== null
            ? sprintf('<style>%s</style>', $fileContents)
            : '';
    }

    public function renderEntryScript(string $entryName): string
    {
        if (!$this->renderAssets) {
            return '';
        }

        $filePath = sprintf('%s/js/entry/%s.js', $this->buildDirectory, $entryName);
        $fileContents = $this->getFileContents($filePath);

        return $fileContents !== null
            ? sprintf('<script>%s</script>', $fileContents)
            : '';
    }

    /**
     * @param string[] $componentTypes
     */
    public function renderComponentStyles(array $componentTypes): string
    {
        if (!$this->renderAssets) {
            return '';
        }

        $content = '';

        foreach ($componentTypes as $componentType) {
            $componentType = (new UnicodeString($componentType))->camel()->toString();

            if ($this->assetsRenderedRegistry->isComponentStyleRendered($componentType)) {
                continue;
            }

            $filePath = sprintf('%s/css/component/%s.css', $this->buildDirectory, $componentType);
            $fileContents = $this->getFileContents($filePath);

            if ($fileContents !== null) {
                $content .= sprintf('<style>%s</style>', $fileContents);
            }

            $this->assetsRenderedRegistry->registerComponentStyleRender($componentType);
        }

        return $content;
    }

    /**
     * @param string[] $componentTypes
     */
    public function renderComponentScripts(array $componentTypes): string
    {
        if (!$this->renderAssets) {
            return '';
        }

        $content = '';

        foreach ($componentTypes as $componentType) {
            $componentType = (new UnicodeString($componentType))->camel()->toString();

            if ($this->assetsRenderedRegistry->isComponentScriptRendered($componentType)) {
                continue;
            }

            $filePath = sprintf('%s/js/component/%s.js', $this->buildDirectory, $componentType);
            $fileContents = $this->getFileContents($filePath);

            if ($fileContents !== null) {
                $content .= sprintf('<script>%s</script>', $fileContents);
            }

            $this->assetsRenderedRegistry->registerComponentScriptRender($componentType);
        }

        return $content;
    }

    private function getFileContents(string $filePath): ?string
    {
        if (!file_exists($filePath)) {
            $this->logger->alert('Could not find assets file "{file}"', ['file' => $filePath]);

            return null;
        }

        $fileContents = file_get_contents($filePath);

        if ($fileContents !== false) {
            return trim($fileContents);
        }

        $this->logger->alert('Could not read assets file "{file}"', ['file' => $filePath]);

        return null;
    }
}

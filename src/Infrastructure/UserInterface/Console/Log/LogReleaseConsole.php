<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Console\Log;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\ElasticsearchApiClient\Client\Document\Index\Request\IndexRequest;
use Visymo\ElasticsearchApiClient\Client\ElasticsearchApiClientInterface;

#[AsCommand(
    name: 'app:log:release'
)]
class LogReleaseConsole extends Command
{
    private const string ARGUMENT_PROJECT_VERSION = 'project_version';

    public function __construct(
        private readonly ElasticsearchApiClientInterface $elasticsearchApiClient
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument(
            name: self::ARGUMENT_PROJECT_VERSION,
            mode: InputArgument::REQUIRED
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $projectVersion = $input->getArgument(self::ARGUMENT_PROJECT_VERSION);

        $this->elasticsearchApiClient->index(
            new IndexRequest(
                document: [
                              '@timestamp' => (new \DateTimeImmutable('now'))->format('Y-m-d\TH:i:s.uP'),
                              'event_type' => 'release',
                              'version'    => $projectVersion,
                              'project'    => 'artemis',
                          ],
                target  : 'release-log',
            )
        );

        return Command::SUCCESS;
    }
}

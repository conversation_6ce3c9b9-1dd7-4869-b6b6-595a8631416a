<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Console\UrlScan;

use Application\Command\UrlScan\ScanUrl\ScanUrlCommand;
use Domain\UrlScan\UrlScanRepositoryInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;

#[AsCommand(
    name       : 'app:url-scan',
    description: 'Scan all URLs for content changes',
)]
final class UrlScanConsole extends Command
{
    public function __construct(
        private readonly CommandBusInterface $commandBus,
        private readonly UrlScanRepositoryInterface $urlScanRepository
    )
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        foreach ($this->urlScanRepository->findAll() as $urlScan) {
            $this->commandBus->handle(
                new ScanUrlCommand($urlScan->id)
            );
        }

        return Command::SUCCESS;
    }
}

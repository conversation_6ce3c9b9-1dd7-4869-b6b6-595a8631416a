<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Console\BrandConfigChecksum;

use Application\Command\Brand\UpdateAllBrandConfigChecksums\UpdateAllBrandConfigChecksumsCommand;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;

#[AsCommand(
    name       : 'app:brand:update-config-checksum',
    description: 'Update config checksum between Antelope and brand website'
)]
final class UpdateBrandConfigChecksumConsole extends Command
{
    private const string ARGUMENT_ANTELOPE_FILE_EQUALS_PRODUCTION = 'prod_equals_file';

    public function __construct(
        private readonly CommandBusInterface $commandBus
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addArgument(
            self::ARGUMENT_ANTELOPE_FILE_EQUALS_PRODUCTION,
            InputArgument::OPTIONAL,
            'Use Antelope file checksum as production checksum',
            "0"
        );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $useAntelopeFileChecksumAsProduction = (bool)$input->getArgument(
            self::ARGUMENT_ANTELOPE_FILE_EQUALS_PRODUCTION
        );

        $this->commandBus->handle(
            new UpdateAllBrandConfigChecksumsCommand($useAntelopeFileChecksumAsProduction)
        );

        return Command::SUCCESS;
    }
}

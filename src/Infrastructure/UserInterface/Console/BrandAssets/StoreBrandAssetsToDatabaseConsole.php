<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Console\BrandAssets;

use Domain\Brand\Brand;
use Domain\Brand\BrandRepositoryInterface;
use Domain\BrandAssets\BrandAssets;
use Domain\BrandAssets\BrandAssetsRepositoryInterface;
use Infrastructure\Persistence\BrandAssets\BrandAssetsFileRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

#[AsCommand(
    name       : 'app:brand-assets:store-to-database',
    description: 'Stores all brand assets files to the database'
)]
class StoreBrandAssetsToDatabaseConsole extends Command
{
    public function __construct(
        private readonly BrandAssetsFileRepository $brandAssetsFileRepository,
        private readonly BrandAssetsRepositoryInterface $brandAssetsRepository,
        private readonly BrandRepositoryInterface $brandRepository
    )
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        foreach ($this->brandAssetsFileRepository->iterate() as $assetsFile) {
            if (preg_match('~^([a-z0-9]+)$~', $assetsFile->getBaseName(), $matches) !== 1) {
                continue;
            }

            $slug = $matches[1];

            $output->writeln(
                sprintf(
                    'Importing <info>%s</info> assets <comment>%s</comment>',
                    $slug,
                    $assetsFile->getRealFilePath()
                )
            );

            $brandAssets = $this->brandAssetsRepository->findOneBySlug($slug);

            if ($brandAssets === null) {
                $brandAssets = null;
                $brand = $this->brandRepository->findOneBySlug($slug);

                if ($brand === null) {
                    $output->writeln(
                        sprintf('Brand "%s" does not exist', $slug)
                    );

                    continue;
                }
            }

            $brand ??= $brandAssets?->brand;

            if ($brand === null) {
                $output->writeln(
                    sprintf('Could not determine brand for "%s"', $slug)
                );

                continue;
            }

            $this->handleAssetsFile($brand, $brandAssets, $assetsFile);
        }

        return Command::SUCCESS;
    }

    private function handleAssetsFile(Brand $brand, ?BrandAssets $brandAssets, SerializedFileInterface $file): void
    {
        if ($brandAssets === null) {
            $brandAssets = new BrandAssets($file->getContents());
            $brandAssets->brand = $brand;
        } else {
            $brandAssets->config = $file->getContents();
        }

        $this->brandAssetsRepository->store($brandAssets);
    }
}

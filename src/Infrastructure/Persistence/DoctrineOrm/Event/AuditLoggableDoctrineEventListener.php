<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Event;

use Application\Command\Generic\LogEntityChange\LogEntityChangeCommand;
use Doctrine\Bundle\DoctrineBundle\Attribute\AsDoctrineListener;
use Doctrine\ORM\EntityManager;
use Doctrine\ORM\Event\PostPersistEventArgs;
use Doctrine\ORM\Event\PostRemoveEventArgs;
use Doctrine\ORM\Event\PostUpdateEventArgs;
use Doctrine\ORM\Event\PreRemoveEventArgs;
use Doctrine\ORM\Events;
use Doctrine\Persistence\Event\LifecycleEventArgs;
use Domain\AuditLog\AuditLog;
use Domain\AuditLog\AuditLogAction;
use Domain\AuditLog\AuditLoggableInterface;
use Infrastructure\UserInterface\Web\Security\Helper\UserHelperInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

#[AsDoctrineListener(event: Events::postPersist)]
#[AsDoctrineListener(event: Events::postUpdate)]
#[AsDoctrineListener(event: Events::preRemove)]
#[AsDoctrineListener(event: Events::postRemove)]
readonly class AuditLoggableDoctrineEventListener
{
    public function __construct(
        private CommandBusInterface $commandBus,
        private UserHelperInterface $userHelper,
        private DateTimeFactory $dateTimeFactory,
        private DoctrineAuditLoggableEntityRegistry $doctrineAuditLoggableEntityRegistry
    )
    {
    }

    public function postPersist(PostPersistEventArgs $args): void
    {
        $this->handleLifeCycleEvent($args, AuditLogAction::INSERT);
    }

    public function postUpdate(PostUpdateEventArgs $args): void
    {
        $this->handleLifeCycleEvent($args, AuditLogAction::UPDATE);
    }

    public function preRemove(PreRemoveEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof AuditLoggableInterface) {
            return;
        }

        $this->doctrineAuditLoggableEntityRegistry->registerEntity($entity);
    }

    public function postRemove(PostRemoveEventArgs $args): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof AuditLoggableInterface) {
            return;
        }

        // TODO SF-1918: remove manual logging in UpdateVbbStylesForBrandHandler
        if ($entity->getAuditLogEntityName() === 'brand.vbb_style_id') {
            return;
        }

        $auditLogEntityId = $this->doctrineAuditLoggableEntityRegistry->getAuditLogEntityId($entity);

        if ($auditLogEntityId === null) {
            return;
        }

        $this->logEntityChange(
            $entity,
            $auditLogEntityId,
            AuditLogAction::DELETE,
            []
        );
    }

    private function handleLifeCycleEvent(LifecycleEventArgs $args, AuditLogAction $action): void
    {
        $entity = $args->getObject();

        if (!$entity instanceof AuditLoggableInterface) {
            return;
        }

        /** @var EntityManager $entityManager */
        $entityManager = $args->getObjectManager();
        $entityData = $entityManager->getUnitOfWork()->getEntityChangeSet($args->getObject());

        if ($entityData === []) {
            return;
        }

        $entityData = $entity->modifyAuditLog($entityData);

        $this->logEntityChange(
            $entity,
            $entity->getAuditLogEntityId(),
            $action,
            $entityData
        );
    }

    /**
     * @param mixed[] $entityData
     */
    private function logEntityChange(
        AuditLoggableInterface $entity,
        int $auditLogEntityId,
        AuditLogAction $action,
        array $entityData
    ): void
    {
        $this->commandBus->handle(
            new LogEntityChangeCommand(
                objectId   : $auditLogEntityId,
                objectType : $entity->getAuditLogEntityName(),
                objectLabel: $entity->getAuditLogEntityLabel(),
                action     : $action,
                userId     : $this->userHelper->getNullableUser()->id ?? AuditLog::SYSTEM_USER,
                entityData : $entityData,
                changedAt  : $this->dateTimeFactory->createNow(TimezoneEnum::UTC)
            )
        );
    }
}

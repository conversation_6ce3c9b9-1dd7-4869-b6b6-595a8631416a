<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\JavaScriptRelatedTerms\JavaScriptRelatedTerms" table="javascript_related_terms">
        <id name="brand" association-key="true" column="brand_id"/>

        <field name="enabled" type="boolean">
            <options>
                <option name="default">0</option>
            </options>
        </field>

        <field name="defaultStyleId" type="google_adsense_style_id" nullable="true"/>
        <field name="contentEnabled" type="boolean">
            <options>
                <option name="default">0</option>
            </options>
        </field>
        <field name="contentClickRoute" type="search_route" length="255" nullable="true"/>

        <field name="searchEnabled" type="boolean">
            <options>
                <option name="default">0</option>
            </options>
        </field>
        <field name="searchClickRoute" type="search_route" length="255" nullable="true"/>

        <one-to-one field="brand" target-entity="Domain\Brand\Brand" inversed-by="javaScriptRelatedTerms">
            <join-column name="brand_id" referenced-column-name="id" nullable="false" on-delete="CASCADE"/>
        </one-to-one>
    </entity>
</doctrine-mapping>

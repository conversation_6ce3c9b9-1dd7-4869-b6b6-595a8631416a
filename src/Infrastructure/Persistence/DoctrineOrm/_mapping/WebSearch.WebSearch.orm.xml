<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\WebSearch\WebSearch" table="web_search">
        <id name="brand" column="brand_id" association-key="true"/>

        <field name="enabled" type="boolean">
            <options>
                <option name="default">0</option>
            </options>
        </field>

        <field name="styleIdDesktop" type="google_adsense_style_id" nullable="true"/>
        <field name="styleIdMobile" type="google_adsense_style_id" nullable="true"/>

        <one-to-one field="brand" target-entity="Domain\Brand\Brand" inversed-by="webSearch">
            <join-column name="brand_id" referenced-column-name="id" nullable="false" on-delete="CASCADE"/>
        </one-to-one>
    </entity>
</doctrine-mapping>

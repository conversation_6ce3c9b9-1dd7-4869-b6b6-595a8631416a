<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\UrlScan\UrlScan" table="url_scan">
        <id name="id" type="smallint">
            <generator/>
            <options>
                <option name="unsigned">true</option>
            </options>
        </id>

        <field name="url" type="string" length="255" unique="true">
            <options>
                <option name="collation">utf8mb4_bin</option>
            </options>
        </field>
        <field name="lastCheckedAt" type="datetime_immutable" nullable="true"/>
        <field name="lastNewContentFoundAt" type="datetime_immutable" nullable="true"/>
    </entity>
</doctrine-mapping>

<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\JsonTemplate\JsonTemplate" table="json_template">
        <id name="brand" association-key="true" column="brand_id"/>

        <field name="templateVariant" type="string" length="255" nullable="true"/>
        <field name="templateOverrides" type="text" length="65535" nullable="true"/>

        <one-to-one field="brand" target-entity="Domain\Brand\Brand" inversed-by="jsonTemplate">
            <join-column name="brand_id" referenced-column-name="id" nullable="false" on-delete="CASCADE"/>
        </one-to-one>
    </entity>
</doctrine-mapping>

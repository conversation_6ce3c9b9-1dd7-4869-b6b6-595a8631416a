<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\User\User" table="user">
        <id name="id" type="smallint">
            <generator/>
            <options>
                <option name="unsigned">true</option>
            </options>
        </id>

        <field name="email" unique="true" length="255"/>
        <field name="firstName" length="255"/>
        <field name="lastName" length="255"/>
        <field name="theme" type="user_theme" length="255">
            <options>
                <option name="default">light</option>
            </options>
        </field>
    </entity>
</doctrine-mapping>

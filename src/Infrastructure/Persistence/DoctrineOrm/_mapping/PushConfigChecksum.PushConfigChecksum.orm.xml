<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\PushConfigChecksum\PushConfigChecksum" table="push_config_checksum">
        <id name="id" type="smallint">
            <generator/>
            <options>
                <option name="unsigned">true</option>
            </options>
        </id>

        <field name="name" type="string" length="255"/>
        <field name="checksum" type="string" length="32">
            <options>
                <option name="fixed">true</option>
                <option name="collation">utf8mb4_bin</option>
            </options>
        </field>
        <field name="lastPushedAt" type="datetime"/>

        <unique-constraints>
            <unique-constraint columns="name"/>
        </unique-constraints>
    </entity>
</doctrine-mapping>

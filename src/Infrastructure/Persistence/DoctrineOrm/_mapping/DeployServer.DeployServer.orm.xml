<doctrine-mapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns="http://doctrine-project.org/schemas/orm/doctrine-mapping"
                  xsi:schemaLocation="http://doctrine-project.org/schemas/orm/doctrine-mapping
                          https://www.doctrine-project.org/schemas/orm/doctrine-mapping.xsd">
    <entity name="Domain\DeployServer\DeployServer" table="deploy_server">
        <id name="id" type="integer">
            <generator/>
            <options>
                <option name="unsigned">true</option>
            </options>
        </id>

        <field name="name" type="string" length="255"/>
        <field name="startedAt" type="datetime" nullable="true"/>
        <field name="endedAt" type="datetime" nullable="true"/>
        <field name="updatedAt" type="datetime"/>

        <unique-constraints>
            <unique-constraint columns="deploy_project_id,name"/>
        </unique-constraints>

        <many-to-one field="project" target-entity="Domain\DeployProject\DeployProject" inversed-by="servers">
            <join-column name="deploy_project_id" referenced-column-name="id" nullable="false" on-delete="CASCADE"/>
        </many-to-one>

        <many-to-one field="activeBuild" target-entity="Domain\DeployBuild\DeployBuild">
            <join-column name="active_build_id" referenced-column-name="id" nullable="true" on-delete="SET NULL"/>
        </many-to-one>

        <many-to-many field="availableBuilds" target-entity="Domain\DeployBuild\DeployBuild">
            <join-table name="deploy_server_build">
                <join-columns>
                    <join-column name="deploy_server_id" referenced-column-name="id" on-delete="CASCADE"/>
                </join-columns>
                <inverse-join-columns>
                    <join-column name="deploy_build_id" referenced-column-name="id" on-delete="CASCADE"/>
                </inverse-join-columns>
            </join-table>
        </many-to-many>
    </entity>
</doctrine-mapping>

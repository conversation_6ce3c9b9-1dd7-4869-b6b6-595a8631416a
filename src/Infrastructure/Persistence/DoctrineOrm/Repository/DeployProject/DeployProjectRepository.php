<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\DeployProject;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\DeployProject\DeployProject;
use Domain\DeployProject\DeployProjectRepositoryInterface;
use Domain\DeployProject\Exception\DeployProjectAlreadyExistsException;

final readonly class DeployProjectRepository implements DeployProjectRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneByGitLabProjectId(int $gitLabProjectId): ?DeployProject
    {
        return $this->getRepository()->findOneBy(
            [
                'gitLabProjectId' => $gitLabProjectId,
            ]
        );
    }

    public function findOneByName(string $name): ?DeployProject
    {
        return $this->getRepository()->findOneBy(
            [
                'name' => $name,
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function findAll(): array
    {
        return $this->getRepository()->findAll();
    }

    public function store(DeployProject $deployProject): void
    {
        try {
            $this->entityManager->persist($deployProject);
            $this->entityManager->flush();
        } catch (UniqueConstraintViolationException $exception) {
            throw DeployProjectAlreadyExistsException::create($deployProject->name, $exception);
        }
    }

    /**
     * @return EntityRepository<DeployProject>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(DeployProject::class);
    }
}

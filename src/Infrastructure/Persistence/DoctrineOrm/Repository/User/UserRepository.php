<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\User;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\User\Exception\UserAlreadyExistsException;
use Domain\User\Exception\UserNotFoundException;
use Domain\User\User;
use Domain\User\UserRepositoryInterface;

final readonly class UserRepository implements UserRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneById(int $id): User
    {
        $user = $this->getRepository()->find($id);

        if ($user === null) {
            throw UserNotFoundException::create();
        }

        return $user;
    }

    public function findOneByEmail(string $email): ?User
    {
        return $this->getRepository()->findOneBy(
            [
                'email' => $email,
            ]
        );
    }

    public function store(User $user): void
    {
        try {
            $this->entityManager->persist($user);
            $this->entityManager->flush();
        } catch (UniqueConstraintViolationException $exception) {
            throw UserAlreadyExistsException::create($user->email, $exception);
        }
    }

    /**
     * @return EntityRepository<User>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(User::class);
    }
}

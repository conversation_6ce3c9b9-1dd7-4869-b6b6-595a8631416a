<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\AntelopeBrand;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\AntelopeBrand\AntelopeBrand;
use Domain\AntelopeBrand\AntelopeBrandRepositoryInterface;

readonly class AntelopeBrandRepository implements AntelopeBrandRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneBySlug(string $slug): ?AntelopeBrand
    {
        return $this->getRepository()
            ->createQueryBuilder('antelope_brand')
            ->innerJoin('antelope_brand.brand', 'brand')
            ->where('brand.slug = :slug')
            ->setParameter('slug', $slug)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function store(AntelopeBrand $antelopeBrand): void
    {
        $this->entityManager->persist($antelopeBrand);
        $this->entityManager->flush();
    }

    /**
     * @return EntityRepository<AntelopeBrand>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(AntelopeBrand::class);
    }
}

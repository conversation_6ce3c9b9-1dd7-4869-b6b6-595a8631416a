<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\PushBrandsChecksum;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\PushConfigChecksum\PushConfigChecksum;
use Domain\PushConfigChecksum\PushConfigChecksumRepositoryInterface;

final readonly class PushConfigChecksumRepository implements PushConfigChecksumRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneByName(string $name): ?PushConfigChecksum
    {
        return $this->getRepository()->findOneBy(
            [
                'name' => $name,
            ]
        );
    }

    public function store(PushConfigChecksum $pushConfigChecksum): void
    {
        $this->entityManager->persist($pushConfigChecksum);
        $this->entityManager->flush();
    }

    /**
     * @return EntityRepository<PushConfigChecksum>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(PushConfigChecksum::class);
    }
}

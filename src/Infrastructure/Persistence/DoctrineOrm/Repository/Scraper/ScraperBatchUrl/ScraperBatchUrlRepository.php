<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\Scraper\ScraperBatchUrl;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrl;
use Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlRepositoryInterface;

final readonly class ScraperBatchUrlRepository implements ScraperBatchUrlRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    /**
     * @return EntityRepository<ScraperBatchUrl>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(ScraperBatchUrl::class);
    }

    public function findByScraperBatchId(int $scraperBatchId): \Generator
    {
        $query = $this->getRepository()->createQueryBuilder('scraperBatchUrl')
            ->where('scraperBatchUrl.scraperBatch = :scraperBatchId')
            ->setParameter('scraperBatchId', $scraperBatchId)
            ->getQuery();

        $results = $query->toIterable();

        foreach ($results as $result) {
            yield $result;
        }
    }

    public function findByScraperBatchIdAndRelatedShown(int $scraperBatchId, bool $relatedShown): \Generator
    {
        $query = $this->getRepository()->createQueryBuilder('scraperBatchUrl')
            ->where('scraperBatchUrl.scraperBatch = :scraperBatchId')
            ->andWhere('scraperBatchUrl.isRelatedShown = :relatedShown')
            ->setParameter('scraperBatchId', $scraperBatchId)
            ->setParameter('relatedShown', $relatedShown)
            ->getQuery();

        $results = $query->toIterable();

        foreach ($results as $result) {
            yield $result;
        }
    }

    public function countByScraperBatchIdAndRelatedShown(int $scraperBatchId, ?bool $relatedShown = null): int
    {
        $criteria = [
            'scraperBatch' => $scraperBatchId,
        ];

        if ($relatedShown !== null) {
            $criteria['isRelatedShown'] = $relatedShown;
        }

        return $this->getRepository()->count($criteria);
    }

    public function store(ScraperBatchUrl $scraperBatchUrl): void
    {
        $this->entityManager->persist($scraperBatchUrl);
        $this->entityManager->flush();
    }

    /**
     * @inheritDoc
     */
    public function storeAll(array $scraperBatchUrls): void
    {
        foreach ($scraperBatchUrls as $scraperBatchUrl) {
            $this->entityManager->persist($scraperBatchUrl);
        }

        $this->entityManager->flush();
    }

    public function updateIsRelatedShownForBatch(int $scraperBatchId, bool $isRelatedShown): void
    {
        $this->getRepository()
            ->createQueryBuilder('scraperBatchUrl')
            ->update()
            ->set('scraperBatchUrl.isRelatedShown', ':isRelatedShown')
            ->where('scraperBatchUrl.scraperBatch = :scraperBatchId')
            ->setParameter('isRelatedShown', $isRelatedShown)
            ->setParameter('scraperBatchId', $scraperBatchId)
            ->getQuery()
            ->execute();
    }
}

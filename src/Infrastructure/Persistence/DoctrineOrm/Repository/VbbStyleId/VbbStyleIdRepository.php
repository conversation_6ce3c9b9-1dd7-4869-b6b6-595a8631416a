<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\VbbStyleId;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\VbbStyleId\Exception\VbbStyleIdAlreadyExistsException;
use Domain\VbbStyleId\VbbStyleId;
use Domain\VbbStyleId\VbbStyleIdRepositoryInterface;

final readonly class VbbStyleIdRepository implements VbbStyleIdRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneByStyleId(AdSenseStyleId $styleId): ?VbbStyleId
    {
        return $this->getRepository()->findOneBy(
            [
                'styleId' => $styleId,
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function findBySlug(string $slug): array
    {
        return $this->getRepository()
            ->createQueryBuilder('vbb_style_id')
            ->innerJoin('vbb_style_id.brand', 'brand')
            ->where('brand.slug = :slug')
            ->setParameter('slug', $slug)
            ->getQuery()
            ->getResult();
    }

    public function delete(VbbStyleId $vbbStyleId): void
    {
        $this->entityManager->remove($vbbStyleId);
        $this->entityManager->flush();
    }

    public function store(VbbStyleId $vbbStyleId): void
    {
        try {
            $this->entityManager->persist($vbbStyleId);
            $this->entityManager->flush();
        } catch (UniqueConstraintViolationException) {
            throw VbbStyleIdAlreadyExistsException::create((string)$vbbStyleId->styleId);
        }
    }

    /**
     * @return EntityRepository<VbbStyleId>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(VbbStyleId::class);
    }

    /**
     * @inheritDoc
     */
    public function findAll(): array
    {
        return $this->getRepository()->findAll();
    }
}

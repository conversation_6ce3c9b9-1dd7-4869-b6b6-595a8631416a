<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\ConversionException;
use Doctrine\DBAL\Types\DateTimeType;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

class UtcDateTimeType extends DateTimeType
{
    public function convertToDatabaseValue(mixed $value, AbstractPlatform $platform): ?string
    {
        if ($value instanceof \DateTime) {
            $value->setTimezone(TimezoneEnum::UTC->toDateTimeZone());
        }

        return parent::convertToDatabaseValue($value, $platform);
    }

    public function convertToPHPValue(mixed $value, AbstractPlatform $platform): ?\DateTime
    {
        if ($value === null || $value instanceof \DateTime) {
            return $value;
        }

        $converted = \DateTime::createFromFormat(
            $platform->getDateTimeFormatString(),
            $value,
            TimezoneEnum::UTC->toDateTimeZone()
        );

        if ($converted === false) {
            // This message logic removed from doctrine ORM in v 4.0.
            $value = strlen($value) > 32 ? substr($value, 0, 20).'...' : $value;

            throw new ConversionException(
                sprintf(
                    'Could not convert database value "%s" to Doctrine Type %s. Expected format: %s',
                    $value,
                    self::class,
                    $platform->getDateTimeFormatString()
                )
            );
        }

        return $converted;
    }
}

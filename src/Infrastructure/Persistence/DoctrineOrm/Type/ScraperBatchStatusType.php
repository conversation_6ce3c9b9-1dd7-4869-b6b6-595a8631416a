<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\StringType;
use Domain\Scraper\ScraperBatch\ScraperBatchStatus;

class ScraperBatchStatusType extends StringType
{
    public function convertToPHPValue(mixed $value, AbstractPlatform $platform): ?ScraperBatchStatus
    {
        if ($value === null) {
            return null;
        }

        if (!is_string($value)) {
            return null;
        }

        return ScraperBatchStatus::from($value);
    }

    public function convertToDatabaseValue(mixed $value, AbstractPlatform $platform): ?string
    {
        if ($value === null) {
            return null;
        }

        return $value instanceof ScraperBatchStatus ? $value->value : $value;
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Type;

use Doctrine\DBAL\Platforms\AbstractPlatform;
use Doctrine\DBAL\Types\StringType;
use Domain\AdSenseStyleId\AdSenseStyleId;

class GoogleAdSenseStyleIdType extends StringType
{
    /**
     * @inheritDoc
     */
    public function getSQLDeclaration(array $column, AbstractPlatform $platform): string
    {
        $column['length'] = 10;
        $column['fixed'] = true;

        return parent::getSQLDeclaration($column, $platform);
    }

    public function convertToPHPValue(mixed $value, AbstractPlatform $platform): ?AdSenseStyleId
    {
        if ($value === null) {
            return null;
        }

        if (!is_string($value)) {
            return null;
        }

        return AdSenseStyleId::fromStyleId($value);
    }

    public function convertToDatabaseValue(mixed $value, AbstractPlatform $platform): ?string
    {
        if ($value === null) {
            return null;
        }

        if (!$value instanceof AdSenseStyleId) {
            throw new \RuntimeException(
                sprintf(
                    'Could not map value of type "%s" to %s',
                    gettype($value),
                    AdSenseStyleId::class
                )
            );
        }

        return (string)$value;
    }
}

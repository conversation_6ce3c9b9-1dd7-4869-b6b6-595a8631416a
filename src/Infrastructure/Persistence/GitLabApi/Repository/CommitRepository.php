<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\GitLabApi\Repository;

use Domain\GitLabApi\Commit\Commit;
use Domain\GitLabApi\Commit\CommitRepositoryInterface;
use Infrastructure\Service\GitLabApi\GitLabApiClientInterface;
use Infrastructure\Service\GitLabApi\Request\Pagination\PaginationRequestFactory;
use Infrastructure\Service\GitLabApi\Resource\Commit\GetCommits\GetCommitsRequest;
use Infrastructure\Service\GitLabApi\Response\Exception\GitLabApiClientException;

final readonly class CommitRepository implements CommitRepositoryInterface
{
    public function __construct(
        private GitLabApiClientInterface $gitLabApiClient,
        private PaginationRequestFactory $paginationRequestFactory
    )
    {
    }

    public function findLatestMasterCommit(int $projectId): Commit
    {
        $paginationRequest = $this->paginationRequestFactory->create(
            page   : 1,
            perPage: 1
        );
        $request = new GetCommitsRequest(
            projectId        : $projectId,
            refName          : 'master',
            firstParent      : true,
            paginationRequest: $paginationRequest
        );

        $commit = $this->gitLabApiClient
            ->commit()
            ->getCommits($request)
            ->getFirstCommit();

        if ($commit === null) {
            throw new GitLabApiClientException('No master commit found');
        }

        return $commit;
    }
}

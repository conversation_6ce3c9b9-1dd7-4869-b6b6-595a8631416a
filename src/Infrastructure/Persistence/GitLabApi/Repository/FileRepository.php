<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\GitLabApi\Repository;

use Domain\GitLabApi\File\File;
use Domain\GitLabApi\File\FileRepositoryInterface;
use Infrastructure\Service\GitLabApi\GitLabApiClientInterface;
use Infrastructure\Service\GitLabApi\Resource\File\GetFile\GetFileRequest;

final readonly class FileRepository implements FileRepositoryInterface
{
    public function __construct(
        private GitLabApiClientInterface $gitLabApiClient
    )
    {
    }

    public function getForPath(int $projectId, string $path, string $ref = 'master'): File
    {
        $request = new GetFileRequest(
            projectId: $projectId,
            path     : $path,
            ref      : $ref
        );

        return $this->gitLabApiClient
            ->file()
            ->getFile($request)
            ->getFile();
    }
}

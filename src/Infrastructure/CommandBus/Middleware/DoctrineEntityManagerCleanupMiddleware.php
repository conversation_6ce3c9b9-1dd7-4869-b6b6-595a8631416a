<?php

declare(strict_types=1);

namespace Infrastructure\CommandBus\Middleware;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandBusEnvelope;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandMiddlewareFrameInterface;
use Visymo\CommandBus\Domain\CommandBus\CommandMiddlewareInterface;

readonly class DoctrineEntityManagerCleanupMiddleware implements CommandMiddlewareInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private LoggerInterface $logger
    )
    {
    }

    /**
     * Sorted from high to low, negative number allowed
     */
    public function getPriority(): int
    {
        // Set to a negative value to force this middleware to be placed as last
        return -1;
    }

    public function handle(
        CommandBusEnvelope $envelope,
        CommandMiddlewareFrameInterface $frame,
        CommandBusInterface $currentCommandBus
    ): void
    {
        try {
            $frame->next(
                envelope: $envelope,
                currentCommandBus: $currentCommandBus
            );
        } finally {
            $this->logger->debug('Clearing entity manager');
            $this->entityManager->clear();
        }
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\Service\JsonTemplateOverrides;

use Domain\GitLabApi\Tree\TreeRepositoryInterface;
use Domain\JsonTemplateOverrides\JsonTemplateOverridesClientInterface;

readonly class JsonTemplateOverridesClient implements JsonTemplateOverridesClientInterface
{
    public function __construct(
        private int $projectId,
        private TreeRepositoryInterface $treeRepository
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function pullJsonTemplateOverrides(): array
    {
        $templateOverrides = [];
        $treeResponse = $this->treeRepository->getForPath(
            projectId: $this->projectId,
            path: 'resources/json_template_overrides',
            recursive: true
        );

        foreach ($treeResponse as $tree) {
            preg_match(
                '~resources/json_template_overrides/(?P<group>[^/]+)(?:/(?P<module>[^/]+)(?:/(?P<template>[^/]+))?)?~',
                $tree->path,
                $matches
            );

            // ignore groups without templates
            if (isset($matches['group'], $matches['module'], $matches['template'])) {
                $templateOverrides[$matches['group']] ??= [];
                $templateOverrides[$matches['group']][$matches['module']] ??= [];
                $templateOverrides[$matches['group']][$matches['module']][] = $matches['template'];
            }
        }

        return $templateOverrides;
    }
}

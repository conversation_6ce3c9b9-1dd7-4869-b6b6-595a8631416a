<?php

declare(strict_types=1);

namespace Infrastructure\Service\JsonTemplateOverrides;

use Infrastructure\Service\JsonTemplateOverrides\Exception\JsonTemplateOverrideDoesNotExistsException;
use Visymo\Filesystem\SerializedFile\SerializedFileInterface;

class JsonTemplateOverrides
{
    /** @var array<string, array<string,string[]>> */
    private array $templateOverrides;

    public function __construct(
        private readonly SerializedFileInterface $templateOverridesJsonFile
    )
    {
    }

    /**
     * @return string[]
     */
    public function getAllOverrideNames(): array
    {
        return array_keys($this->getConfig());
    }

    /**
     * @return string[]
     *
     * @throws JsonTemplateOverrideDoesNotExistsException
     */
    public function getOverrideJsonTemplates(string $overrideName): array
    {
        $modules = $this->getConfig()[$overrideName] ?? null;

        if ($modules === null) {
            throw JsonTemplateOverrideDoesNotExistsException::create($overrideName);
        }

        $jsonTemplates = [];

        foreach ($modules as $module => $moduleJsonTemplates) {
            foreach ($moduleJsonTemplates as $jsonTemplate) {
                $jsonTemplates[] = sprintf('%s/%s', $module, $jsonTemplate);
            }
        }

        return $jsonTemplates;
    }

    /**
     * @return array<string, array<string,string[]>>
     */
    private function getConfig(): array
    {
        if (!isset($this->templateOverrides)) {
            $this->templateOverrides = $this->templateOverridesJsonFile->getContents();
        }

        return $this->templateOverrides;
    }
}

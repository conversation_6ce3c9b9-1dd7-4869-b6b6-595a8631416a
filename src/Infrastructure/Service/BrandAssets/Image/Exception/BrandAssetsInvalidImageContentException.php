<?php

declare(strict_types=1);

namespace Infrastructure\Service\BrandAssets\Image\Exception;

class BrandAssetsInvalidImageContentException extends BrandAssetsImageException
{
    public static function create(string $name, ?\Throwable $previous = null): self
    {
        return new self(
            sprintf('Invalid base64 image format for "%s"', $name),
            0,
            $previous
        );
    }
}

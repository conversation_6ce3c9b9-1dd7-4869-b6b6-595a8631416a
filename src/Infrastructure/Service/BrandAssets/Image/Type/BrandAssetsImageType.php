<?php

declare(strict_types=1);

namespace Infrastructure\Service\BrandAssets\Image\Type;

enum BrandAssetsImageType: string
{
    case JPG = 'jpg';
    case ICO = 'ico';
    case PNG = 'png';
    case SVG = 'svg';

    public static function fromBase64Value(string $base64ImageType): self
    {
        if ($base64ImageType === 'svg+xml') {
            return self::SVG;
        }

        return self::from($base64ImageType);
    }
}

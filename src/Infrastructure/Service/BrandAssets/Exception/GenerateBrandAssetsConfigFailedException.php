<?php

declare(strict_types=1);

namespace Infrastructure\Service\BrandAssets\Exception;

class GenerateBrandAssetsConfigFailedException extends \RuntimeException
{
    public function __construct(
        string $message,
        ?\Throwable $previous = null
    )
    {
        parent::__construct(
            sprintf('Failed to generate brand assets config from ZIP file: %s ', $message),
            0,
            $previous
        );
    }
}

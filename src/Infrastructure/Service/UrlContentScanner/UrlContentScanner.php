<?php

declare(strict_types=1);

namespace Infrastructure\Service\UrlContentScanner;

use Domain\UrlScan\Exception\UrlContentScanFailedException;
use Domain\UrlScan\Scanner\UrlContentScannerInterface;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;

final readonly class UrlContentScanner implements UrlContentScannerInterface
{
    public function __construct(
        private ClientInterface $client,
        private RequestFactoryInterface $httpRequestFactory
    )
    {
    }

    public function getContent(string $url): string
    {
        try {
            $httpRequest = $this->httpRequestFactory->createRequest('GET', $url);
            $response = $this->client->sendRequest($httpRequest);

            return $response->getBody()->getContents();
        } catch (\Throwable $exception) {
            throw UrlContentScanFailedException::create($url, $exception);
        }
    }
}

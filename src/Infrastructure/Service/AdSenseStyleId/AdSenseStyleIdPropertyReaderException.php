<?php

declare(strict_types=1);

namespace Infrastructure\Service\AdSenseStyleId;

use Visymo\Shared\Domain\Exception\ExceptionWithContextInterface;

class AdSenseStyleIdPropertyReaderException extends \RuntimeException implements ExceptionWithContextInterface
{
    private string $styleId;

    public static function create(
        string $message,
        string $styleId,
        ?\Throwable $previous = null
    ): self
    {
        $instance = new self($message, 0, $previous);
        $instance->styleId = $styleId;

        return $instance;
    }

    /**
     * @inheritDoc
     */
    public function getContext(): array
    {
        return [
            'style_id' => $this->styleId,
        ];
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\Service\DataFilter\Filter;

use Domain\Brand\Brand;
use Domain\VbbStyleId\VbbStyleId;
use Infrastructure\Service\DataFilter\Collection\DataFilterCollection;
use Infrastructure\Service\DataFilter\ExpressionNode\StringExpressionNode;

final readonly class VbbDataFilter implements DataFilterInterface
{
    private const string NAME_STYLE_ID = 'styleId';
    private const string NAME_SLUG     = 'slug';

    public function getCollection(): DataFilterCollection
    {
        return DataFilterCollection::VBB;
    }

    /**
     * @inheritDoc
     */
    public function getExpressionNodes(): array
    {
        return [
            StringExpressionNode::create(self::NAME_STYLE_ID, 'Style ID'),
            StringExpressionNode::create(self::NAME_SLUG, 'Brand slug'),
        ];
    }

    /**
     * @inheritDoc
     */
    public function getItemValues(mixed $item): array
    {
        if (!$item instanceof VbbStyleId) {
            return [];
        }

        return [
            self::NAME_STYLE_ID => $item->styleId,
            self::NAME_SLUG     => $item->brand->slug,
        ];
    }

    public function canHandleExpressionAsQuery(string $expression): bool
    {
        $pattern = sprintf('/%s/', Brand::SLUG_REGEX);

        return preg_match($pattern, $expression) === 1;
    }

    public function doesItemMatchesQuery(mixed $item, string $query): bool
    {
        if (!$item instanceof VbbStyleId) {
            return false;
        }

        return str_contains($item->brand->slug, $query);
    }
}

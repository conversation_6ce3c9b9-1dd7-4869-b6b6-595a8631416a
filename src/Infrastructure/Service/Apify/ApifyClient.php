<?php

declare(strict_types=1);

namespace Infrastructure\Service\Apify;

use Domain\Scraper\Exception\InvalidStatusCodeException;
use Domain\Scraper\Exception\InvalidStatusException;
use Domain\Scraper\ScraperBatch\ScraperBatchStatus;
use Domain\Scraper\ScraperClientInterface;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestFactoryInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamFactoryInterface;

readonly class ApifyClient implements ScraperClientInterface
{
    private const array  DEFAULT_APIFY_INPUT = [
        'pageFunction'       => 'async ({ request, page }) => { console.log(request.url); }',
        'preNavigationHooks' => "[async ({ page }) => { await page.setUserAgent('ApifyBot/1.0'); ".
                                "await page.setExtraHTTPHeaders({'X-Is-Apify': '1'}); }]",
    ];

    private const int MEMORY_LIMIT = 4096;

    public function __construct(
        private string $token,
        private string $apiUrl,
        private ClientInterface $client,
        private RequestFactoryInterface $requestFactory,
        private StreamFactoryInterface $streamFactory
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function scheduleRun(array $urls): string
    {
        $body = self::DEFAULT_APIFY_INPUT;
        $body['startUrls'] = [];

        foreach ($urls as $url) {
            $body['startUrls'][] = ['url' => $url];
        }

        $request = $this->requestFactory->createRequest(
            'POST',
            sprintf('%s/acts/apify~puppeteer-scraper/runs?token=%s&memory=%d', $this->apiUrl, $this->token, self::MEMORY_LIMIT),
        );

        $request = $request->withAddedHeader('Content-Type', 'application/json');
        $stream = $this->streamFactory->createStream(json_encode($body, JSON_UNESCAPED_SLASHES | JSON_THROW_ON_ERROR));
        $request = $request->withBody($stream);

        $response = $this->sendRequestWithAuthorization($request);

        if ($response->getStatusCode() !== 201) {
            throw InvalidStatusCodeException::create($response->getStatusCode(), $this->getName());
        }

        $responseArray = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

        return $responseArray['data']['id'];
    }

    public function getRunStatus(string $runId): ScraperBatchStatus
    {
        $request = $this->requestFactory->createRequest(
            'GET',
            sprintf('%s/actor-runs/%s?token=%s', $this->apiUrl, $runId, $this->token),
        );

        $response = $this->sendRequestWithAuthorization($request);

        $responseArray = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR);

        $status = ScraperBatchStatus::tryFrom(strtolower($responseArray['data']['status']));

        if ($status === null) {
            throw InvalidStatusException::create($responseArray['data']['status'], $this->getName());
        }

        return $status;
    }

    /**
     * Only runs that are starting or running are aborted.
     * For runs with status FINISHED, FAILED, ABORTING and TIMED-OUT this call does nothing.
     */
    public function abortRun(string $runId): void
    {
        $request = $this->requestFactory->createRequest(
            'POST',
            sprintf('%s/actor-runs/%s/abort?token=%s', $this->apiUrl, $runId, $this->token),
        );

        $response = $this->sendRequestWithAuthorization($request);

        if ($response->getStatusCode() !== 200) {
            throw InvalidStatusCodeException::create($response->getStatusCode(), $this->getName());
        }
    }

    public function getName(): string
    {
        return 'apify';
    }

    private function sendRequestWithAuthorization(RequestInterface $request): ResponseInterface
    {
        $request = $request->withAddedHeader('Authorization', sprintf('Bearer %s', $this->token));

        return $this->client->sendRequest($request);
    }
}

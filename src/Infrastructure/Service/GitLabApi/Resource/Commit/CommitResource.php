<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Commit;

use Infrastructure\Service\GitLabApi\Resource\Commit\GetCommits\GetCommitsRequest;
use Infrastructure\Service\GitLabApi\Resource\Commit\GetCommits\GetCommitsResponse;
use Infrastructure\Service\GitLabApi\Response\ResponseFactory;

final readonly class CommitResource
{
    public function __construct(
        private ResponseFactory $responseFactory,
        private CommitResponseFactory $commitResponseFactory
    )
    {
    }

    public function getCommits(GetCommitsRequest $request): GetCommitsResponse
    {
        return $this->commitResponseFactory->createGetCommits(
            $this->responseFactory->create($request)
        );
    }
}

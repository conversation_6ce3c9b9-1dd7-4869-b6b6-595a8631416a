<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Commit\GetCommits;

use Domain\GitLabApi\Commit\Commit;
use Domain\GitLabApi\Commit\CommitFactory;
use Infrastructure\Service\GitLabApi\Response\Response;

final class GetCommitsResponse
{
    public function __construct(
        private readonly Response $response,
        private readonly CommitFactory $commitFactory
    )
    {
    }

    public function getFirstCommit(): ?Commit
    {
        foreach ($this->response->getRows() as $row) {
            return $this->commitFactory->createFromArray($row);
        }

        return null;
    }

    /**
     * @return \Generator<Commit>
     */
    public function getCommits(): \Generator
    {
        foreach ($this->response->getRows() as $row) {
            yield $this->commitFactory->createFromArray($row);
        }
    }
}

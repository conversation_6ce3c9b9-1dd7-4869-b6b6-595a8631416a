<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Job;

use Infrastructure\Service\GitLabApi\Resource\Job\GetPipelineJobs\GetPipelineJobsRequest;
use Infrastructure\Service\GitLabApi\Resource\Job\GetPipelineJobs\GetPipelineJobsResponse;
use Infrastructure\Service\GitLabApi\Resource\Job\RunJob\RunJobRequest;
use Infrastructure\Service\GitLabApi\Resource\Job\RunJob\RunJobResponse;
use Infrastructure\Service\GitLabApi\Response\ResponseFactory;

final readonly class JobResource
{
    public function __construct(
        private ResponseFactory $responseFactory,
        private JobResponseFactory $jobResponseFactory
    )
    {
    }

    public function getPipelineJobs(GetPipelineJobsRequest $request): GetPipelineJobsResponse
    {
        return $this->jobResponseFactory->createGetPipelineJobs(
            $this->responseFactory->create($request)
        );
    }

    public function runJob(RunJobRequest $request): RunJobResponse
    {
        return $this->jobResponseFactory->createRunJob(
            $this->responseFactory->create($request)
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\File\GetFile;

use Domain\GitLabApi\File\File;
use Domain\GitLabApi\File\FileFactory;
use Infrastructure\Service\GitLabApi\Response\Response;

final readonly class GetFileResponse
{
    public function __construct(
        private Response $response,
        private FileFactory $fileFactory
    )
    {
    }

    public function getFile(): File
    {
        return $this->fileFactory->create(
            $this->response->getData()
        );
    }
}

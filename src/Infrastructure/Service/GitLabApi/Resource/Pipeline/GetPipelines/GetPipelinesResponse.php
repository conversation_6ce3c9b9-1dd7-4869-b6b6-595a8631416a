<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Pipeline\GetPipelines;

use Domain\GitLabApi\Pipeline\Pipeline;
use Domain\GitLabApi\Pipeline\PipelineFactory;
use Infrastructure\Service\GitLabApi\Response\Response;

final class GetPipelinesResponse
{
    public function __construct(
        private readonly Response $response,
        private readonly PipelineFactory $pipelineFactory
    )
    {
    }

    public function getFirstPipeline(): ?Pipeline
    {
        foreach ($this->response->getRows() as $row) {
            return $this->pipelineFactory->createFromArray($row);
        }

        return null;
    }

    /**
     * @return \Generator<Pipeline>
     */
    public function getPipelines(): \Generator
    {
        foreach ($this->response->getRows() as $row) {
            yield $this->pipelineFactory->createFromArray($row);
        }
    }
}

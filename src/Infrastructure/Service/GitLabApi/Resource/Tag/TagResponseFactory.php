<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Tag;

use Domain\GitLabApi\Tag\TagFactory;
use Infrastructure\Service\GitLabApi\Resource\Tag\GetTags\GetTagsResponse;
use Infrastructure\Service\GitLabApi\Response\Response;

final readonly class TagResponseFactory
{
    public function __construct(
        private TagFactory $tagFactory
    )
    {
    }

    public function createGetTags(Response $response): GetTagsResponse
    {
        return new GetTagsResponse(
            response  : $response,
            tagFactory: $this->tagFactory
        );
    }
}

<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Tree;

use Infrastructure\Service\GitLabApi\Resource\Tree\GetTree\GetTreeRequest;
use Infrastructure\Service\GitLabApi\Resource\Tree\GetTree\GetTreeResponse;
use Infrastructure\Service\GitLabApi\Response\ResponseFactory;

final readonly class TreeResource
{
    public function __construct(
        private ResponseFactory $responseFactory,
        private TreeResponseFactory $treeResponseFactory
    )
    {
    }

    public function getTree(GetTreeRequest $request): GetTreeResponse
    {
        return $this->treeResponseFactory->createGetTree(
            $this->responseFactory->create($request)
        );
    }
}

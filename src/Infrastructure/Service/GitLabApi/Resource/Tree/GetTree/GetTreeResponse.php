<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Tree\GetTree;

use Domain\GitLabApi\Tree\Tree;
use Domain\GitLabApi\Tree\TreeFactory;
use Infrastructure\Service\GitLabApi\Response\Response;

final readonly class GetTreeResponse
{
    public function __construct(
        private Response $response,
        private TreeFactory $treeFactory
    )
    {
    }

    /**
     * @return \Generator<Tree>
     */
    public function getTree(): \Generator
    {
        foreach ($this->response->getRows() as $row) {
            yield $this->treeFactory->createFromArray($row);
        }
    }
}

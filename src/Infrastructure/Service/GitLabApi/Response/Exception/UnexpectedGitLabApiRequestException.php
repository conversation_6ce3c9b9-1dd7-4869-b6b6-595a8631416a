<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Response\Exception;

use Visymo\Shared\Domain\Exception\ExceptionWithContextInterface;

class UnexpectedGitLabApiRequestException extends GitLabApiClientException implements ExceptionWithContextInterface
{
    private string $url;

    public static function create(string $url, ?\Throwable $previous = null): self
    {
        $instance = new self(
            'Encountered unexpected exception during GitLab API request',
            0,
            $previous
        );
        $instance->url = $url;

        return $instance;
    }

    /**
     * @inheritDoc
     */
    public function getContext(): array
    {
        return [
            'url' => $this->url,
        ];
    }
}

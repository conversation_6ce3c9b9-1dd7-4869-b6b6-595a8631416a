<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Response\Pagination;

final class PaginationResponseFactory
{
    private const string KEY_NEXT_PAGE   = 'x-next-page';
    private const string KEY_PAGE        = 'x-page';
    private const string KEY_PER_PAGE    = 'x-per-page';
    private const string KEY_PREV_PAGE   = 'x-prev-page';
    private const string KEY_TOTAL       = 'x-total';
    private const string KEY_TOTAL_PAGES = 'x-total-pages';

    /**
     * @param array<string, int|null> $data
     */
    public function createFromArray(array $data): PaginationResponse
    {
        return new PaginationResponse(
            nextPage    : $this->getNullableInt($data, self::KEY_NEXT_PAGE),
            page        : $this->getNullableInt($data, self::KEY_PAGE),
            perPage     : $this->getNullableInt($data, self::KEY_PER_PAGE),
            previousPage: $this->getNullableInt($data, self::KEY_PREV_PAGE),
            total       : $this->getNullableInt($data, self::KEY_TOTAL),
            totalPages  : $this->getNullableInt($data, self::KEY_TOTAL_PAGES),
        );
    }

    /**
     * @param array<string, mixed[]|int|null> $data
     */
    private function getNullableInt(array $data, string $key): ?int
    {
        $value = $data[$key] ?? null;
        $value = is_array($value) ? current($value) : $value;

        return $value !== null ? (int)$value : null;
    }
}

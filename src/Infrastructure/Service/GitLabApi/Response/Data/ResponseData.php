<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Response\Data;

final class ResponseData
{
    /** @var mixed[] */
    private array $headers;

    /**
     * @param mixed[] $data
     * @param mixed[] $headers
     */
    public function __construct(
        public readonly array $data,
        array $headers
    )
    {
        $this->headers = array_change_key_case($headers, CASE_LOWER);
    }

    public function getNextPage(): ?int
    {
        $nextPage = $this->headers['x-next-page'][0] ?? 0;
        $nextPage = (int)$nextPage;

        return $nextPage > 0 ? $nextPage : null;
    }
}

<?php

declare(strict_types=1);

namespace Framework\LambdaTest\Capabilities;

use Framework\LambdaTest\Browser\Browser;
use Framework\LambdaTest\Resolution\Resolution;

abstract class AbstractCapabilities implements CapabilitiesInterface
{
    protected const LAMBDA_TEST_HUB_URL_TEMPLATE = 'https://%s:%<EMAIL>/wd/hub';

    protected const DEFAULT_META_CAPABILITIES = [
        'tunnel'   => true,
        'video'    => true,
        'network'  => true, // capture network logs
        'console'  => true, // capture browser console logs
        'terminal' => true, // capture terminal logs
    ];

    /**
     * @param mixed[] $capabilities
     */
    protected function __construct(
        private readonly string $hubUrlTemplate,
        private readonly string $label,
        private readonly Browser $browser,
        private readonly string $platformType,
        private readonly ?Resolution $windowResolution,
        private array $capabilities
    )
    {
    }

    public function setTestDetails(string $build, string $name): void
    {
        $this->capabilities['build'] = $build;
        $this->capabilities['name'] = $name;
    }

    public function getHubUrl(string $username, string $accessKey): string
    {
        return sprintf($this->hubUrlTemplate, $username, $accessKey);
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    public function getBrowser(): Browser
    {
        return $this->browser;
    }

    public function getPlatformType(): string
    {
        return $this->platformType;
    }

    public function getWindowResolution(): ?Resolution
    {
        return $this->windowResolution;
    }

    public function getLabelAsKey(): string
    {
        $key = strtolower($this->getLabel());
        $key = str_replace(' ', '-', $key);
        $key = preg_replace('~([\-]{2,})~', '-', $key);

        return (string)$key;
    }

    /**
     * @inheritDoc
     */
    public function getCapabilities(): array
    {
        return $this->capabilities;
    }
}

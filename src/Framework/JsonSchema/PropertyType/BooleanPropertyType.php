<?php

declare(strict_types=1);

namespace Framework\JsonSchema\PropertyType;

final class BooleanPropertyType implements PropertyTypeInterface
{
    private ?bool $value;

    public function __construct(
        private readonly ?string $name,
        private readonly bool $required,
        private readonly bool $nullable,
        private readonly ?bool $default
    )
    {
    }

    public function getType(): PropertyType
    {
        return PropertyType::BOOLEAN;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function isRequired(): bool
    {
        return $this->required;
    }

    public function isNullable(): bool
    {
        return $this->nullable;
    }

    public function getDefault(): ?bool
    {
        return $this->default;
    }

    public function getValue(): ?bool
    {
        return $this->value;
    }

    public function getNormalizedValue(): ?bool
    {
        if ($this->getValue() === $this->getDefault()) {
            return null;
        }

        return $this->getValue() ?? $this->getDefault();
    }

    public function setValue(mixed $value): void
    {
        $this->value = $value !== null ? (bool)$value : null;
    }
}

<?php

declare(strict_types=1);

namespace Framework\JsonSchema\PropertyType;

final class StringPropertyType implements PropertyTypeInterface, PropertyEnumTypeInterface
{
    private ?string $value;

    /**
     * @param string[] $enumValues
     */
    public function __construct(
        private readonly ?string $name,
        private readonly bool $required,
        private readonly bool $nullable,
        private readonly ?string $default,
        public readonly ?string $pattern,
        private readonly array $enumValues
    )
    {
    }

    public function getType(): PropertyType
    {
        return PropertyType::STRING;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function isRequired(): bool
    {
        return $this->required;
    }

    public function isNullable(): bool
    {
        return $this->nullable;
    }

    public function getDefault(): ?string
    {
        return $this->default;
    }

    public function isEnum(): bool
    {
        return $this->enumValues !== [];
    }

    /**
     * @inheritDoc
     */
    public function getEnumValues(): array
    {
        $enumValues = [];

        if ($this->isNullable()) {
            $enumValues[] = null;
        }

        return [
            ...$enumValues,
            ...$this->enumValues,
        ];
    }

    public function getValue(): ?string
    {
        return $this->value;
    }

    public function getNormalizedValue(): ?string
    {
        if ($this->getValue() === $this->getDefault()) {
            return null;
        }

        return $this->getValue() ?? $this->getDefault();
    }

    public function setValue(mixed $value): void
    {
        $this->value = (string)$value;

        if ($this->value === '') {
            $this->value = null;
        }
    }
}

<?php

declare(strict_types=1);

namespace Framework\JsonSchema;

use Framework\JsonSchema\Cursor\JsonCursor;

interface JsonSchemaInterface
{
    /**
     * @return mixed[]
     */
    public function getData(): array;

    /**
     * @param string $pointer Like '/properties/container'
     *
     * @return mixed[]|null
     */
    public function getDataByPointer(string $pointer): ?array;

    /**
     * @param string $pointer Like '#/definitions/property/property1'
     *
     * @return mixed[]|null
     */
    public function getDataByPropertyPointer(string $pointer): ?array;

    /**
     * @return mixed[]|null
     */
    public function getDataByJsonCursor(JsonCursor $jsonCursor): ?array;
}

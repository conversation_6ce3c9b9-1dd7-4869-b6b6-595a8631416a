<?php

declare(strict_types=1);

namespace Framework\JsonSchema\ObjectDefinition;

use Framework\JsonSchema\PropertyType\PropertyTypeInterface;

final class JsonObjectDefinition
{
    /** @var array<string, mixed> */
    private array $values = [];

    /**
     * @param PropertyTypeInterface[] $properties
     */
    public function __construct(
        public readonly array $properties
    )
    {
    }

    public function hasProperties(): bool
    {
        return $this->properties !== [];
    }

    public function setValue(string $name, mixed $value): void
    {
        $this->values[$name] = $value;
    }

    public function getValue(string $name): mixed
    {
        return $this->values[$name] ?? null;
    }

    /**
     * @return array<string, mixed>
     */
    public function toConfig(): array
    {
        $config = [];

        foreach ($this->properties as $property) {
            $normalizedValue = $property->getNormalizedValue();

            if ($normalizedValue !== null) {
                $config[$property->getName()] = $normalizedValue;
            }
        }

        return $config;
    }
}

<?php

declare(strict_types=1);

namespace Framework\JsonSchema\CursorReplacement;

use Framework\JsonSchema\Cursor\JsonCursor;
use Framework\JsonSchema\JsonSchemaInterface;
use Framework\JsonSchema\ObjectDefinition\JsonObjectDefinitionFactory;

final readonly class JsonCursorReplacementFactory
{
    private const string CURSOR_ROW_REPLACE_VALUE = '__CURSOR_ROW__';

    public function __construct(
        private JsonSchemaInterface $jsonSchema,
        private JsonObjectDefinitionFactory $objectDefinitionFactory
    )
    {
    }

    /**
     * @param array<string, mixed> $objectEditorData
     */
    public function create(JsonCursor $jsonCursor, array $objectEditorData): ?JsonCursorReplacement
    {
        $objectDefinitionSchemaData = $this->jsonSchema->getDataByJsonCursor($jsonCursor);

        if ($objectDefinitionSchemaData === null) {
            return null;
        }

        $objectDefinition = $this->objectDefinitionFactory->create(
            schemaData: $objectDefinitionSchemaData,
            options   : $objectEditorData
        );

        // Split content to insert new object definition
        $beforeContent = substr($jsonCursor->content, 0, $jsonCursor->objectStartIndex);
        $afterContent = substr($jsonCursor->content, $jsonCursor->objectEndIndex + 1);

        // Replace content with the new object definition
        $replaceContent = json_encode($objectDefinition->toConfig(), JSON_THROW_ON_ERROR);
        $content = sprintf('%s%s%s', $beforeContent, $replaceContent, $afterContent);
        $content = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
        $content = json_encode($content, JSON_PRETTY_PRINT | JSON_THROW_ON_ERROR);

        // Find cursor row and column
        $cursorRow = $this->getCursorRow($beforeContent, $afterContent);
        $cursorColumn = $this->getCursorColumn($content, $cursorRow);

        return new JsonCursorReplacement(
            content     : $content,
            cursorRow   : $cursorRow,
            cursorColumn: $cursorColumn
        );
    }

    private function getCursorRow(string $beforeContent, string $afterContent): int
    {
        $replaceContent = json_encode(self::CURSOR_ROW_REPLACE_VALUE, JSON_THROW_ON_ERROR);

        $content = sprintf('%s%s%s', $beforeContent, $replaceContent, $afterContent);
        $content = json_decode($content, true, 512, JSON_THROW_ON_ERROR);
        $content = json_encode($content, JSON_PRETTY_PRINT | JSON_THROW_ON_ERROR);
        $content = substr($content, 0, (int)strpos($content, self::CURSOR_ROW_REPLACE_VALUE));

        return max(0, count(explode("\n", $content)));
    }

    private function getCursorColumn(string $content, int $cursorRow): int
    {
        $line = explode("\n", $content)[$cursorRow - 1] ?? '';

        return (int)strpos($line, '{') + 1;
    }
}
